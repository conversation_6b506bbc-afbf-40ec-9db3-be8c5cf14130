# VPN Client 连接设置文件
# 
# 此文件是使用 VPN Client 管理器导出的。
# 此文件内容可使用文本编辑器进行编辑。
# 当此文件通过使用客户端连接管理器导入后可以立即被使用。

declare root
{
	bool CheckServerCert false
	uint64 CreateDateTime 0
	uint64 LastConnectDateTime 0
	bool StartupAccount true
	uint64 UpdateDateTime 0

	declare ClientAuth
	{
		uint AuthType 1
		byte HashedPassword AAAAAAAAAAAAAAAAAAAAAAAAAAA=
		string Username $
	}
	declare ClientOption
	{
		string AccountName WEBO
		uint AdditionalConnectionInterval 1
		uint ConnectionDisconnectSpan 0
		string DeviceName VPN
		bool DisableQoS false
		bool HalfConnection false
		bool HideNicInfoWindow true
		bool HideStatusWindow false
		string Hostname vpn.szxinjin.cn
		string HubName WEBO
		uint MaxConnection 1
		bool NoRoutingTracking false
		bool NoTls1 false
		bool NoUdpAcceleration false
		uint NumRetry **********
		uint Port 433
		uint PortUDP 0
		string ProxyName $
		byte ProxyPassword $
		uint ProxyPort 0
		uint ProxyType 0
		string ProxyUsername $
		bool RequireBridgeRoutingMode false
		bool RequireMonitorMode false
		uint RetryInterval 15
		bool UseCompress false
		bool UseEncrypt true
	}
}
