{"Version": 1.1, "General": {"FileSameAsSource": true, "SourceCatalog": "", "SourceCloudInstanceName": "", "SourceProject": "", "SourceProjectOwnerNavicatID": "", "SourceSchema": "xinjin_wx85b65e5b87738ac3", "SourceServer": "新晋65", "SourceServerType": "MYSQL", "TargetCatalog": "", "TargetCloudInstanceName": "", "TargetFileFormatEncoding": 65001, "TargetFileFormatServerEdition": 0, "TargetFileFormatServerType": "MYSQL", "TargetFileFormatServerVersion": 80099, "TargetFilePath": "", "TargetProject": "", "TargetProjectOwnerNavicatID": "", "TargetSchema": "xinjin_wx85b65e5b87738ac3", "TargetServer": "本地3306 192.168.3.7", "TargetServerType": "MYSQL", "TransferToFile": false, "ContinueOnError": false, "ConvertObjName": false, "ConvertObjNameUpper": false, "CreateDatabase": true, "CreateIndexes": true, "CreateRecords": true, "CreateTables": true, "CreateTriggers": true, "DropTables": true, "DropWithCascade": false, "IgnoreBlobForFile": false, "IncludeAutoIncrement": true, "IncludeCharset": true, "IncludeChecks": true, "IncludeDefiner": false, "IncludeEngine": true, "IncludeExcludes": true, "IncludeForeignKeys": true, "IncludeOtherTableOptions": false, "IncludeOwnerTo": false, "IncludeRules": true, "IncludeSchemaNameInStatements": false, "IncludeStorage": false, "IncludeUniques": true, "LockSourceTables": false, "LockTargetTables": false, "RunMultipleInsertStatements": true, "UseBlobHex": true, "UseCompleteInsert": true, "UseDDL": false, "UseDelayedInsert": false, "UseExtendedInsert": true, "UseSingleTransaction": false, "UseTransaction": true}, "Tables": {"SelectAll": true, "Selected": []}, "Views": {"SelectAll": false, "Selected": []}, "Functions": {"SelectAll": false, "Selected": []}, "Events": {"SelectAll": false, "Selected": []}}