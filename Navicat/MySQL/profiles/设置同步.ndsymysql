{"Version": 1.2, "General": {"ContinueOnError": false, "IncludeDelete": true, "IncludeInsert": true, "IncludeUpdate": true, "RunMultipleSQL": false, "ShowCompareResultAndDeploymentSQL": true, "ShowDetails": true, "SourceCatalog": "", "SourceCloudInstanceName": "", "SourceProject": "", "SourceProjectOwnerNavicatID": "", "SourceSchema": "xinjin_wxe697fb48a75a2b60", "SourceServer": "本地3306 ***********", "SourceServerType": "MYSQL", "TargetCatalog": "", "TargetCloudInstanceName": "", "TargetProject": "", "TargetProjectOwnerNavicatID": "", "TargetSchema": "xinjin_wx85b65e5b87738ac3", "TargetServer": "新晋65", "TargetServerType": "MYSQL", "UseTransaction": false}, "Tables": [{"Source": {"Name": "coupon_goods", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "coupon_goods", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "coupon_user", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "coupon_user", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "cust_account_base", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "cust_account_base", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "cust_account_detail", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "cust_account_detail", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "cust_apply_manual", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "cust_apply_manual", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "cust_points_records", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "cust_points_records", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "cust_retail", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "cust_retail", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "division_agent", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "division_agent", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "division_approval_records", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "division_approval_records", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "gen_table", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "gen_table", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "gen_table_column", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "gen_table_column", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "goods_category", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "goods_category", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "goods_collection", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "goods_collection", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "goods_spu", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "goods_spu", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "images_category", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "images_category", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "order_info", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "order_info", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "order_item", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "order_item", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "order_logistics", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "order_logistics", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "order_share_profit", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "order_share_profit", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "pay_records", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "pay_records", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "permission", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "permission", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "pintuan_goods", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "pintuan_goods", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "pintuan_info", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "pintuan_info", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "pintuans", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "pintuans", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "pintuans_orders", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "pintuans_orders", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_blob_triggers", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_blob_triggers", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_calendars", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_calendars", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_cron_triggers", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_cron_triggers", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_fired_triggers", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_fired_triggers", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_job_details", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_job_details", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_locks", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_locks", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_paused_trigger_grps", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_paused_trigger_grps", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_scheduler_state", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_scheduler_state", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_simple_triggers", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_simple_triggers", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_simprop_triggers", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_simprop_triggers", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "qrtz_triggers", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "qrtz_triggers", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "rights_cart", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "rights_cart", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "rights_cart_records", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "rights_cart_records", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "rights_list", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "rights_list", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "scene", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "scene", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "seckill_goods", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "seckill_goods", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "shipping_templates", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "shipping_templates", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "shopping_cart", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "shopping_cart", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "staff_application_records", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "staff_application_records", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "store", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "store", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "store_goods_spu", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "store_goods_spu", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_area", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_area", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_article", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_article", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_config", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_config", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_dept", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_dept", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_dict_data", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_dict_data", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_dict_type", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_dict_type", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_job", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_job", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_job_log", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_job_log", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_menu", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_menu", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_notice", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_notice", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_oper_log", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_oper_log", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_post", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_post", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_role", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_role", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_role_dept", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_role_dept", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_role_menu", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_role_menu", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_theme", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_theme", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_user", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_user", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_user_post", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_user_post", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "sys_user_role", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "sys_user_role", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "template_list", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "template_list", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "ui_setting", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "ui_setting", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "user_address", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "user_address", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "user_images", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "user_images", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "user_level", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "user_level", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "venue_goods", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "venue_goods", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "venues", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "venues", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "verify_code_records", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "verify_code_records", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_ad", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_ad", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_apply_retail", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_apply_retail", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_auto_reply", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_auto_reply", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_ma_configs", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_ma_configs", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_menu", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_menu", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_msg", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_msg", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_pay_manual", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_pay_manual", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "wx_user_permission", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "wx_user_permission", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}]}