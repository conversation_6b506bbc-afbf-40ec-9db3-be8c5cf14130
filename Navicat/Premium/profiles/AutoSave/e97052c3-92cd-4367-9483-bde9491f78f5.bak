-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡', '3', '1', 'tags', 'zhule/tags/index', 1, 0, 'C', '0', '0', 'zhule:tags:list', '#', 'admin', sysdate(), '', null, '选项卡菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:export',       '#', 'admin', sysdate(), '', null, '');