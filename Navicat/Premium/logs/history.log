[2025-08-24 17:19:19.95][本地3306 ***********][000006][MYSQL][]
SHOW VARIABLES LIKE 'lower_case_%'; SHOW VARIABLES LIKE 'sql_mode'; SELECT COUNT(*) AS support_ndb FROM information_schema.ENGINES WHERE Engine = 'ndbcluster'

[2025-08-24 17:19:19.971][本地3306 ***********][000006][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 17:19:30.036][本地Mysql8][000509][MYSQL][]
SHOW VARIABLES LIKE 'lower_case_%'; SHOW VARIABLES LIKE 'sql_mode'; SELECT COUNT(*) AS support_ndb FROM information_schema.ENGINES WHERE Engine = 'ndbcluster'

[2025-08-24 17:19:30.045][本地Mysql8][000509][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 17:19:31.058][本地Mysql8][000509][MYSQL][]
SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'izhule' UNION SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'izhule' UNION SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'izhule'

[2025-08-24 17:19:31.071][本地Mysql8][000509][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_SCHEMA, TABLE_TYPE

[2025-08-24 17:19:31.074][本地Mysql8][000509][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_SCHEMA, TABLE_NAME

[2025-08-24 17:19:31.084][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 17:19:31.091][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS

[2025-08-24 17:19:31.117][本地Mysql8][000509][MYSQL][]
SELECT DISTINCT ROUTINE_SCHEMA, ROUTINE_NAME, PARAMS.PARAMETER FROM information_schema.ROUTINES LEFT JOIN ( SELECT SPECIFIC_SCHEMA, SPECIFIC_NAME, GROUP_CONCAT(CONCAT(DATA_TYPE, ' ', PARAMETER_NAME) ORDER BY ORDINAL_POSITION SEPARATOR ', ') PARAMETER, ROUTINE_TYPE FROM information_schema.PARAMETERS GROUP BY SPECIFIC_SCHEMA, SPECIFIC_NAME, ROUTINE_TYPE ) PARAMS ON ROUTINES.ROUTINE_SCHEMA = PARAMS.SPECIFIC_SCHEMA AND ROUTINES.ROUTINE_NAME = PARAMS.SPECIFIC_NAME AND ROUTINES.ROUTINE_TYPE = PARAMS.ROUTINE_TYPE WHERE ROUTINE_SCHEMA = 'izhule' ORDER BY ROUTINE_SCHEMA

[2025-08-24 17:19:36.278][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` LIMIT 0,1000

[2025-08-24 17:19:36.281][本地Mysql8][000509][MYSQL][]
SHOW TABLE STATUS LIKE 'banner'

[2025-08-24 17:19:36.288][本地Mysql8][000511][MYSQL][]
SHOW COLUMNS FROM `izhule`.`banner`

[2025-08-24 17:19:36.29][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `izhule`.`banner`

[2025-08-24 17:19:36.292][本地Mysql8][000511][MYSQL][]
SHOW INDEX FROM `banner`

[2025-08-24 17:19:36.307][本地Mysql8][000509][MYSQL][]
SHOW CREATE TABLE `banner` 

[2025-08-24 17:19:36.308][本地Mysql8][000509][MYSQL][]
SHOW FULL COLUMNS FROM `banner`

[2025-08-24 17:19:42.274][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link` = '/pages/note/detail?id=note_001' WHERE `id` = '1955070605113516033'

[2025-08-24 17:19:42.278][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955070605113516033'

[2025-08-24 17:19:44.208][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link` = '/pages/note/detail?id=note_001' WHERE `id` = '1955081116970475522'

[2025-08-24 17:19:44.214][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955081116970475522'

[2025-08-24 17:19:45.069][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link` = '/pages/note/detail?id=note_001' WHERE `id` = '1955949738722922497'

[2025-08-24 17:19:45.072][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955949738722922497'

[2025-08-24 17:19:47.386][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link` = '/pages/note/detail?id=note_001' WHERE `id` = '1955949779961319426'

[2025-08-24 17:19:47.389][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955949779961319426'

[2025-08-24 17:21:06.346][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link_type` = 'page' WHERE `id` = '1955070605113516033'

[2025-08-24 17:21:06.35][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955070605113516033'

[2025-08-24 17:21:06.35][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link_type` = 'page' WHERE `id` = '1955081116970475522'

[2025-08-24 17:21:06.353][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955081116970475522'

[2025-08-24 17:21:06.354][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link_type` = 'page' WHERE `id` = '1955949738722922497'

[2025-08-24 17:21:06.357][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955949738722922497'

[2025-08-24 17:21:06.357][本地Mysql8][000509][MYSQL][]
UPDATE `izhule`.`banner` SET `link_type` = 'page' WHERE `id` = '1955949779961319426'

[2025-08-24 17:21:06.36][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955949779961319426'

[2025-08-24 17:48:30.293][本地Mysql8][000509][MYSQL][]
SELECT * FROM `izhule`.`note` LIMIT 0,1000

[2025-08-24 17:48:30.294][本地Mysql8][000509][MYSQL][]
SHOW TABLE STATUS LIKE 'note'

[2025-08-24 17:48:30.298][本地Mysql8][000511][MYSQL][]
SHOW COLUMNS FROM `izhule`.`note`

[2025-08-24 17:48:30.3][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `izhule`.`note`

[2025-08-24 17:48:30.302][本地Mysql8][000511][MYSQL][]
SHOW INDEX FROM `note`

[2025-08-24 17:48:30.314][本地Mysql8][000509][MYSQL][]
SHOW CREATE TABLE `note` 

[2025-08-24 17:48:30.315][本地Mysql8][000509][MYSQL][]
SHOW FULL COLUMNS FROM `note`

[2025-08-24 17:51:55.155][本地Mysql8][000509][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 17:51:55.158][本地Mysql8][000509][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 17:51:55.159][本地Mysql8][000509][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 17:51:55.162][本地Mysql8][000509][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 17:51:55.172][本地Mysql8][000509][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 17:51:55.174][本地Mysql8][000509][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 17:51:55.282][本地Mysql8][000509][MYSQL][]
SHOW CHARACTER SET

[2025-08-24 17:51:55.392][本地Mysql8][000509][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 17:51:55.415][本地Mysql8][000509][MYSQL][]
SHOW ENGINES

[2025-08-24 17:51:55.486][本地Mysql8][000509][MYSQL][]
SELECT DISTINCT(TABLESPACE_NAME) AS TABLESPACE_NAME FROM information_schema.FILES WHERE NOT ISNULL(TABLESPACE_NAME) LIMIT 10000

[2025-08-24 17:51:55.68][本地Mysql8][000509][MYSQL][]
SHOW COLLATION

[2025-08-24 18:05:49.356][本地Mysql8][000509][MYSQL][]
SHOW VARIABLES LIKE 'lower_case_%'; SHOW VARIABLES LIKE 'sql_mode'; SELECT COUNT(*) AS support_ndb FROM information_schema.ENGINES WHERE Engine = 'ndbcluster'

[2025-08-24 18:05:49.361][本地Mysql8][000509][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:05:49.417][本地Mysql8][000509][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:05:49.423][本地Mysql8][000509][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-24 18:05:52.838][本地Mysql8][000509][MYSQL][]
SET PROFILING = 1

[2025-08-24 18:05:52.839][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:05:52.851][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:05:52.865][本地Mysql8][000509][MYSQL][]
INSERT INTO note_comment (id, note_id, user_id, parent_id, content, images, create_time, likes, dislikes, reply_count) VALUES
('comment_001', 'note_001', 1, NULL, '这个地方真的很不错，环境很棒！', '["profile/upload/100x100.jpg","profile/upload/200x200.jpg"]', NOW(), 15, 2, 3),
('comment_002', 'note_001', 2, NULL, '价格有点贵，但是服务很好', '["profile/upload/150x150.jpg"]', NOW(), 8, 1, 1),
('comment_003', 'note_002', 3, NULL, '推荐大家来试试，味道很棒', '[]', NOW(), 12, 0, 2),
('comment_004', 'note_002', 4, NULL, '环境一般般，不过菜品还可以', '["profile/upload/300x300.jpg","profile/upload/250x250.jpg","profile/upload/180x180.jpg"]', NOW(), 5, 3, 0),
('comment_005', 'note_003', 5, NULL, '性价比很高，会再来的', '["profile/upload/120x120.jpg"]', NOW(), 20, 1, 4),
('comment_006', 'note_004', 6, NULL, '服务态度需要改进', '[]', NOW(), 3, 8, 1),
('comment_007', 'note_005', 7, NULL, '超级推荐！朋友聚会的好地方', '["profile/upload/160x160.jpg","profile/upload/140x140.jpg"]', NOW(), 25, 0, 2),
('comment_008', 'note_006', 8, NULL, '菜品创新，值得一试', '["profile/upload/220x220.jpg"]', NOW(), 18, 2, 1),
('comment_009', 'note_007', 9, NULL, '位置有点偏，但是值得专程去', '[]', NOW(), 10, 1, 0),
('comment_010', 'note_008', 10, NULL, '整体体验很满意', '["profile/upload/280x280.jpg","profile/upload/190x190.jpg"]', NOW(), 14, 0, 3)

[2025-08-24 18:05:52.866][本地Mysql8][000509][MYSQL][]
Unknown column 'parent_id' in 'field list'

[2025-08-24 18:06:06.16][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_comment'

[2025-08-24 18:06:06.163][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-24 18:06:06.164][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-24 18:06:06.167][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-24 18:06:06.179][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_comment' ORDER BY event_object_table

[2025-08-24 18:06:06.181][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:06:06.281][本地Mysql8][000510][MYSQL][]
SHOW CHARACTER SET

[2025-08-24 18:06:06.385][本地Mysql8][000510][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:06:21.539][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment'

[2025-08-24 18:06:21.542][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-24 18:06:21.544][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-24 18:06:21.547][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-24 18:06:21.561][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment' ORDER BY event_object_table

[2025-08-24 18:06:21.563][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:06:21.748][本地Mysql8][000510][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:06:33.923][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'product\_category'

[2025-08-24 18:06:33.926][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `product_category` 

[2025-08-24 18:06:33.928][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `product_category`

[2025-08-24 18:06:33.931][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `product_category`

[2025-08-24 18:06:33.943][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'product_category' ORDER BY event_object_table

[2025-08-24 18:06:33.945][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'product_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:06:34.151][本地Mysql8][000510][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:06:51.188][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
DROP COLUMN `nickname`,
DROP COLUMN `avatar`

[2025-08-24 18:06:51.253][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:06:51.259][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:06:51.263][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:06:51.264][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:06:51.267][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:06:51.271][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:06:51.273][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:07:52.989][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
ADD COLUMN `parent_id` varchar(36) NULL COMMENT '父ID' AFTER `dislikes`

[2025-08-24 18:07:53.047][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:07:53.052][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:07:53.055][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:07:53.057][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:07:53.06][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:07:53.063][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:07:53.064][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:08:14.323][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`goods_comment` 
ADD COLUMN `parent_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父ID' AFTER `dislikes`

[2025-08-24 18:08:14.398][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:08:14.404][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_comment'

[2025-08-24 18:08:14.406][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-24 18:08:14.408][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-24 18:08:14.411][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-24 18:08:14.414][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_comment' ORDER BY event_object_table

[2025-08-24 18:08:14.416][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:08:22.266][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`goods_comment` 
MODIFY COLUMN `parent_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父ID' AFTER `id`

[2025-08-24 18:08:22.34][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:08:22.346][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_comment'

[2025-08-24 18:08:22.349][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-24 18:08:22.35][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-24 18:08:22.353][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-24 18:08:22.356][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_comment' ORDER BY event_object_table

[2025-08-24 18:08:22.358][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:08:29.274][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
MODIFY COLUMN `parent_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父ID' AFTER `id`

[2025-08-24 18:08:29.334][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:08:29.34][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:08:29.342][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:08:29.344][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:08:29.347][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:08:29.35][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:08:29.352][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:08:33.854][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment'

[2025-08-24 18:08:33.857][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-24 18:08:33.858][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-24 18:08:33.862][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-24 18:08:33.876][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment' ORDER BY event_object_table

[2025-08-24 18:08:33.878][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:08:34.084][本地Mysql8][000510][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:09:33.715][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`merchant_comment` 
ADD COLUMN `parent_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父ID' AFTER `id`

[2025-08-24 18:09:33.782][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:09:33.788][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment'

[2025-08-24 18:09:33.79][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-24 18:09:33.792][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-24 18:09:33.795][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-24 18:09:33.798][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment' ORDER BY event_object_table

[2025-08-24 18:09:33.8][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:10:27.129][本地Mysql8][000509][MYSQL][]
SET PROFILING = 1

[2025-08-24 18:10:27.13][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:10:27.139][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:10:27.149][本地Mysql8][000509][MYSQL][]
parent_id

[2025-08-24 18:10:27.15][本地Mysql8][000509][MYSQL][]
You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'parent_id' at line 1

[2025-08-24 18:10:29.734][本地Mysql8][000509][MYSQL][]
SET PROFILING = 1

[2025-08-24 18:10:29.736][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:10:29.747][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:10:29.76][本地Mysql8][000509][MYSQL][]
INSERT INTO note_comment (id, note_id, user_id, parent_id, content, images, create_time, likes, dislikes, reply_count) VALUES
('comment_001', 'note_001', 1, NULL, '这个地方真的很不错，环境很棒！', '["profile/upload/100x100.jpg","profile/upload/200x200.jpg"]', NOW(), 15, 2, 3),
('comment_002', 'note_001', 2, NULL, '价格有点贵，但是服务很好', '["profile/upload/150x150.jpg"]', NOW(), 8, 1, 1),
('comment_003', 'note_002', 3, NULL, '推荐大家来试试，味道很棒', '[]', NOW(), 12, 0, 2),
('comment_004', 'note_002', 4, NULL, '环境一般般，不过菜品还可以', '["profile/upload/300x300.jpg","profile/upload/250x250.jpg","profile/upload/180x180.jpg"]', NOW(), 5, 3, 0),
('comment_005', 'note_003', 5, NULL, '性价比很高，会再来的', '["profile/upload/120x120.jpg"]', NOW(), 20, 1, 4),
('comment_006', 'note_004', 6, NULL, '服务态度需要改进', '[]', NOW(), 3, 8, 1),
('comment_007', 'note_005', 7, NULL, '超级推荐！朋友聚会的好地方', '["profile/upload/160x160.jpg","profile/upload/140x140.jpg"]', NOW(), 25, 0, 2),
('comment_008', 'note_006', 8, NULL, '菜品创新，值得一试', '["profile/upload/220x220.jpg"]', NOW(), 18, 2, 1),
('comment_009', 'note_007', 9, NULL, '位置有点偏，但是值得专程去', '[]', NOW(), 10, 1, 0),
('comment_010', 'note_008', 10, NULL, '整体体验很满意', '["profile/upload/280x280.jpg","profile/upload/190x190.jpg"]', NOW(), 14, 0, 3)

[2025-08-24 18:10:29.761][本地Mysql8][000509][MYSQL][]
Unknown column 'images' in 'field list'

[2025-08-24 18:11:20.105][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`goods_comment` 
ADD COLUMN `images` json NULL COMMENT '评论图片列表' AFTER `goods_id`

[2025-08-24 18:11:20.106][本地Mysql8][000510][MYSQL][]
Duplicate column name 'images'

[2025-08-24 18:11:26.111][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type = 'VIEW'

[2025-08-24 18:11:26.115][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-24 18:11:40.379][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:11:40.388][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS

[2025-08-24 18:11:43.876][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_comment'

[2025-08-24 18:11:43.879][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-24 18:11:43.88][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-24 18:11:43.884][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-24 18:11:43.886][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_comment' ORDER BY event_object_table

[2025-08-24 18:11:43.888][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:11:44.104][本地Mysql8][000510][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:12:05.375][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
ADD COLUMN `images` json NULL COMMENT '评论图片列表' AFTER `user_id`

[2025-08-24 18:12:05.435][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:12:05.441][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:12:05.444][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:12:05.445][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:12:05.448][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:12:05.451][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:12:05.452][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:12:09.948][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`fun_comment` 
DROP COLUMN `user_nickname`,
DROP COLUMN `user_avatar`

[2025-08-24 18:12:10.035][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:12:10.041][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment'

[2025-08-24 18:12:10.043][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-24 18:12:10.045][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-24 18:12:10.048][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-24 18:12:10.051][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment' ORDER BY event_object_table

[2025-08-24 18:12:10.053][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:12:32.176][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`merchant_comment` 
DROP COLUMN `user_nickname`,
DROP COLUMN `user_avatar`

[2025-08-24 18:12:32.24][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:12:32.246][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment'

[2025-08-24 18:12:32.249][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-24 18:12:32.25][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-24 18:12:32.253][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-24 18:12:32.256][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment' ORDER BY event_object_table

[2025-08-24 18:12:32.258][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:12:39.015][本地Mysql8][000509][MYSQL][]
SET PROFILING = 1

[2025-08-24 18:12:39.016][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:12:39.025][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:12:39.036][本地Mysql8][000509][MYSQL][]
INSERT INTO note_comment (id, note_id, user_id, parent_id, content, images, create_time, likes, dislikes, reply_count) VALUES
('comment_001', 'note_001', 1, NULL, '这个地方真的很不错，环境很棒！', '["profile/upload/100x100.jpg","profile/upload/200x200.jpg"]', NOW(), 15, 2, 3),
('comment_002', 'note_001', 2, NULL, '价格有点贵，但是服务很好', '["profile/upload/150x150.jpg"]', NOW(), 8, 1, 1),
('comment_003', 'note_002', 3, NULL, '推荐大家来试试，味道很棒', '[]', NOW(), 12, 0, 2),
('comment_004', 'note_002', 4, NULL, '环境一般般，不过菜品还可以', '["profile/upload/300x300.jpg","profile/upload/250x250.jpg","profile/upload/180x180.jpg"]', NOW(), 5, 3, 0),
('comment_005', 'note_003', 5, NULL, '性价比很高，会再来的', '["profile/upload/120x120.jpg"]', NOW(), 20, 1, 4),
('comment_006', 'note_004', 6, NULL, '服务态度需要改进', '[]', NOW(), 3, 8, 1),
('comment_007', 'note_005', 7, NULL, '超级推荐！朋友聚会的好地方', '["profile/upload/160x160.jpg","profile/upload/140x140.jpg"]', NOW(), 25, 0, 2),
('comment_008', 'note_006', 8, NULL, '菜品创新，值得一试', '["profile/upload/220x220.jpg"]', NOW(), 18, 2, 1),
('comment_009', 'note_007', 9, NULL, '位置有点偏，但是值得专程去', '[]', NOW(), 10, 1, 0),
('comment_010', 'note_008', 10, NULL, '整体体验很满意', '["profile/upload/280x280.jpg","profile/upload/190x190.jpg"]', NOW(), 14, 0, 3)

[2025-08-24 18:12:39.037][本地Mysql8][000509][MYSQL][]
Unknown column 'reply_count' in 'field list'

[2025-08-24 18:13:11.217][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
ADD COLUMN `reply_count` int NULL DEFAULT 0 COMMENT '回复数量' AFTER `dislikes`

[2025-08-24 18:13:11.28][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:13:11.286][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:13:11.289][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:13:11.29][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:13:11.293][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:13:11.296][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:13:11.297][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:13:55.306][本地Mysql8][000509][MYSQL][]
SET PROFILING = 1

[2025-08-24 18:13:55.307][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:13:55.316][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:13:55.327][本地Mysql8][000509][MYSQL][]
INSERT INTO note_comment (id, note_id, user_id, parent_id, content, images, create_time, likes, dislikes, reply_count) VALUES
('comment_001', 'note_001', 1, NULL, '这个地方真的很不错，环境很棒！', '["profile/upload/100x100.jpg","profile/upload/200x200.jpg"]', NOW(), 15, 2, 3),
('comment_002', 'note_001', 2, NULL, '价格有点贵，但是服务很好', '["profile/upload/150x150.jpg"]', NOW(), 8, 1, 1),
('comment_003', 'note_002', 3, NULL, '推荐大家来试试，味道很棒', '[]', NOW(), 12, 0, 2),
('comment_004', 'note_002', 4, NULL, '环境一般般，不过菜品还可以', '["profile/upload/300x300.jpg","profile/upload/250x250.jpg","profile/upload/180x180.jpg"]', NOW(), 5, 3, 0),
('comment_005', 'note_003', 5, NULL, '性价比很高，会再来的', '["profile/upload/120x120.jpg"]', NOW(), 20, 1, 4),
('comment_006', 'note_004', 6, NULL, '服务态度需要改进', '[]', NOW(), 3, 8, 1),
('comment_007', 'note_005', 7, NULL, '超级推荐！朋友聚会的好地方', '["profile/upload/160x160.jpg","profile/upload/140x140.jpg"]', NOW(), 25, 0, 2),
('comment_008', 'note_006', 8, NULL, '菜品创新，值得一试', '["profile/upload/220x220.jpg"]', NOW(), 18, 2, 1),
('comment_009', 'note_007', 9, NULL, '位置有点偏，但是值得专程去', '[]', NOW(), 10, 1, 0),
('comment_010', 'note_008', 10, NULL, '整体体验很满意', '["profile/upload/280x280.jpg","profile/upload/190x190.jpg"]', NOW(), 14, 0, 3)

[2025-08-24 18:13:55.328][本地Mysql8][000509][MYSQL][]
Incorrect integer value: 'note_001' for column 'note_id' at row 1

[2025-08-24 18:14:12.564][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
MODIFY COLUMN `note_id` varchar NOT NULL COMMENT '笔记ID' AFTER `parent_id`

[2025-08-24 18:14:12.564][本地Mysql8][000510][MYSQL][]
You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NOT NULL COMMENT '笔记ID' AFTER `parent_id`' at line 2

[2025-08-24 18:14:18.79][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
MODIFY COLUMN `note_id` varchar NOT NULL COMMENT '笔记ID' AFTER `parent_id`

[2025-08-24 18:14:18.791][本地Mysql8][000510][MYSQL][]
You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NOT NULL COMMENT '笔记ID' AFTER `parent_id`' at line 2

[2025-08-24 18:14:27.821][本地Mysql8][000510][MYSQL][]
SELECT * FROM `izhule`.`note_comment` LIMIT 0,1000

[2025-08-24 18:14:27.822][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note_comment'

[2025-08-24 18:14:27.829][本地Mysql8][000527][MYSQL][]
SHOW COLUMNS FROM `izhule`.`note_comment`

[2025-08-24 18:14:27.831][本地Mysql8][000511][MYSQL][]
SHOW CREATE TABLE `izhule`.`note_comment`

[2025-08-24 18:14:27.833][本地Mysql8][000527][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:14:27.835][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:14:29.958][本地Mysql8][000510][MYSQL][]
SELECT * FROM `izhule`.`note_comment` LIMIT 0,1000

[2025-08-24 18:14:29.959][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note_comment'

[2025-08-24 18:14:29.962][本地Mysql8][000527][MYSQL][]
SHOW COLUMNS FROM `izhule`.`note_comment`

[2025-08-24 18:14:29.964][本地Mysql8][000511][MYSQL][]
SHOW CREATE TABLE `izhule`.`note_comment`

[2025-08-24 18:14:29.966][本地Mysql8][000527][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:14:29.968][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:14:41.684][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:14:41.687][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:14:41.689][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:14:41.692][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:14:41.694][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:14:41.696][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:14:41.899][本地Mysql8][000510][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 18:14:53.038][本地Mysql8][000510][MYSQL][]
ALTER TABLE `izhule`.`note_comment` 
MODIFY COLUMN `note_id` varchar(36) NOT NULL COMMENT '笔记ID' AFTER `parent_id`

[2025-08-24 18:14:53.113][本地Mysql8][000510][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 18:14:53.119][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-24 18:14:53.121][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-24 18:14:53.122][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-24 18:14:53.126][本地Mysql8][000510][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-24 18:14:53.128][本地Mysql8][000510][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-24 18:14:53.13][本地Mysql8][000510][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-24 18:15:00.664][本地Mysql8][000509][MYSQL][]
SET PROFILING = 1

[2025-08-24 18:15:00.665][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:15:00.674][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:15:00.686][本地Mysql8][000509][MYSQL][]
INSERT INTO note_comment (id, note_id, user_id, parent_id, content, images, create_time, likes, dislikes, reply_count) VALUES
('comment_001', 'note_001', 1, NULL, '这个地方真的很不错，环境很棒！', '["profile/upload/100x100.jpg","profile/upload/200x200.jpg"]', NOW(), 15, 2, 3),
('comment_002', 'note_001', 2, NULL, '价格有点贵，但是服务很好', '["profile/upload/150x150.jpg"]', NOW(), 8, 1, 1),
('comment_003', 'note_002', 3, NULL, '推荐大家来试试，味道很棒', '[]', NOW(), 12, 0, 2),
('comment_004', 'note_002', 4, NULL, '环境一般般，不过菜品还可以', '["profile/upload/300x300.jpg","profile/upload/250x250.jpg","profile/upload/180x180.jpg"]', NOW(), 5, 3, 0),
('comment_005', 'note_003', 5, NULL, '性价比很高，会再来的', '["profile/upload/120x120.jpg"]', NOW(), 20, 1, 4),
('comment_006', 'note_004', 6, NULL, '服务态度需要改进', '[]', NOW(), 3, 8, 1),
('comment_007', 'note_005', 7, NULL, '超级推荐！朋友聚会的好地方', '["profile/upload/160x160.jpg","profile/upload/140x140.jpg"]', NOW(), 25, 0, 2),
('comment_008', 'note_006', 8, NULL, '菜品创新，值得一试', '["profile/upload/220x220.jpg"]', NOW(), 18, 2, 1),
('comment_009', 'note_007', 9, NULL, '位置有点偏，但是值得专程去', '[]', NOW(), 10, 1, 0),
('comment_010', 'note_008', 10, NULL, '整体体验很满意', '["profile/upload/280x280.jpg","profile/upload/190x190.jpg"]', NOW(), 14, 0, 3)

[2025-08-24 18:15:00.69][本地Mysql8][000509][MYSQL][]
-- 回复评论
INSERT INTO note_comment (id, note_id, user_id, parent_id, content, images, create_time, likes, dislikes, reply_count) VALUES
-- note_001的回复
('reply_001', 'note_001', 11, 'comment_001', '同意！我也觉得环境很棒', '[]', NOW(), 5, 0, 0),
('reply_002', 'note_001', 12, 'comment_001', '确实，特别是晚上的氛围', '["profile/upload/110x110.jpg"]', NOW(), 3, 0, 0),
('reply_003', 'note_001', 13, 'comment_001', '下次约朋友一起去', '[]', NOW(), 2, 0, 0),
('reply_004', 'note_001', 14, 'comment_002', '贵是贵了点，但物有所值', '[]', NOW(), 4, 0, 0),

-- note_002的回复
('reply_005', 'note_002', 15, 'comment_003', '哪道菜最推荐？', '[]', NOW(), 2, 0, 0),
('reply_006', 'note_002', 16, 'comment_003', '我觉得他们家的招牌菜不错', '["profile/upload/130x130.jpg"]', NOW(), 6, 0, 0),

-- note_003的回复
('reply_007', 'note_003', 17, 'comment_005', '确实性价比高，我也经常去', '[]', NOW(), 8, 0, 0),
('reply_008', 'note_003', 18, 'comment_005', '团购的话更划算', '[]', NOW(), 4, 0, 0),
('reply_009', 'note_003', 19, 'comment_005', '周末人会比较多，建议预约', '["profile/upload/170x170.jpg"]', NOW(), 3, 0, 0),
('reply_010', 'note_003', 20, 'comment_005', '他们家的甜品也很不错', '[]', NOW(), 5, 0, 0),

-- note_004的回复
('reply_011', 'note_004', 21, 'comment_006', '可能是新店，服务还在磨合', '[]', NOW(), 2, 0, 0),

-- note_005的回复
('reply_012', 'note_005', 22, 'comment_007', '是的！我们上次聚会就在那里', '["profile/upload/240x240.jpg"]', NOW(), 7, 0, 0),
('reply_013', 'note_005', 23, 'comment_007', '包间环境很好，适合商务聚餐', '[]', NOW(), 4, 0, 0),

-- note_006的回复
('reply_014', 'note_006', 24, 'comment_008', '创新菜品确实有特色', '[]', NOW(), 3, 0, 0),

-- note_008的回复
('reply_015', 'note_008', 25, 'comment_010', '服务员态度很好', '[]', NOW(), 2, 0, 0),
('reply_016', 'note_008', 26, 'comment_010', '环境也很干净', '["profile/upload/260x260.jpg"]', NOW(), 3, 0, 0),
('reply_017', 'note_008', 27, 'comment_010', '会推荐给朋友的', '[]', NOW(), 1, 0, 0)

[2025-08-24 18:15:00.694][本地Mysql8][000509][MYSQL][]
SHOW STATUS

[2025-08-24 18:15:00.703][本地Mysql8][000509][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-24 18:15:00.705][本地Mysql8][000509][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.000334*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=12 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-24 18:28:08.218][本地Mysql8][000510][MYSQL][]
SELECT * FROM `izhule`.`sys_user` LIMIT 0,1000

[2025-08-24 18:28:08.219][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_user'

[2025-08-24 18:28:08.223][本地Mysql8][000527][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_user`

[2025-08-24 18:28:08.226][本地Mysql8][000511][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_user`

[2025-08-24 18:28:08.227][本地Mysql8][000527][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-24 18:28:08.233][本地Mysql8][000510][MYSQL][]
SHOW CREATE TABLE `sys_user` 

[2025-08-24 18:28:08.234][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-24 20:08:41.45][本地Mysql8][000510][MYSQL][]
SELECT * FROM `izhule`.`sys_user` LIMIT 0,1000

[2025-08-24 20:08:41.455][本地Mysql8][000510][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_user'

[2025-08-24 20:08:41.461][本地Mysql8][000527][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_user`

[2025-08-24 20:08:41.466][本地Mysql8][000511][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_user`

[2025-08-24 20:08:41.467][本地Mysql8][000527][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-24 20:08:41.473][本地Mysql8][000510][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-24 20:08:50.358][腾讯_上门按摩][004253][MYSQL][]
SHOW VARIABLES LIKE 'lower_case_%'; SHOW VARIABLES LIKE 'sql_mode'; SELECT COUNT(*) AS support_ndb FROM information_schema.ENGINES WHERE Engine = 'ndbcluster'

[2025-08-24 20:08:50.374][腾讯_上门按摩][004253][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-24 20:09:12.768][腾讯_上门按摩][004253][MYSQL][]
SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'anmo' UNION SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'anmo' UNION SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'anmo'

[2025-08-24 20:09:12.784][腾讯_上门按摩][004253][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'anmo' ORDER BY TABLE_SCHEMA, TABLE_TYPE

[2025-08-24 20:09:12.796][腾讯_上门按摩][004253][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'anmo' ORDER BY TABLE_SCHEMA, TABLE_NAME

[2025-08-24 20:09:12.83][腾讯_上门按摩][004254][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-24 20:09:12.851][腾讯_上门按摩][004253][MYSQL][]
SELECT DISTINCT ROUTINE_SCHEMA, ROUTINE_NAME, PARAMS.PARAMETER FROM information_schema.ROUTINES LEFT JOIN ( SELECT SPECIFIC_SCHEMA, SPECIFIC_NAME, GROUP_CONCAT(CONCAT(DATA_TYPE, ' ', PARAMETER_NAME) ORDER BY ORDINAL_POSITION SEPARATOR ', ') PARAMETER, ROUTINE_TYPE FROM information_schema.PARAMETERS GROUP BY SPECIFIC_SCHEMA, SPECIFIC_NAME, ROUTINE_TYPE ) PARAMS ON ROUTINES.ROUTINE_SCHEMA = PARAMS.SPECIFIC_SCHEMA AND ROUTINES.ROUTINE_NAME = PARAMS.SPECIFIC_NAME AND ROUTINES.ROUTINE_TYPE = PARAMS.ROUTINE_TYPE WHERE ROUTINE_SCHEMA = 'anmo' ORDER BY ROUTINE_SCHEMA

[2025-08-24 20:09:12.858][腾讯_上门按摩][004254][MYSQL][]
SHOW TABLE STATUS

[2025-08-24 20:09:16.407][腾讯_上门按摩][004253][MYSQL][]
SELECT * FROM `anmo`.`ls_user` LIMIT 0,1000

[2025-08-24 20:09:16.419][腾讯_上门按摩][004253][MYSQL][]
SHOW TABLE STATUS LIKE 'ls_user'

[2025-08-24 20:09:16.491][腾讯_上门按摩][004255][MYSQL][]
SHOW COLUMNS FROM `anmo`.`ls_user`

[2025-08-24 20:09:16.508][腾讯_上门按摩][004254][MYSQL][]
SHOW CREATE TABLE `anmo`.`ls_user`

[2025-08-24 20:09:16.525][腾讯_上门按摩][004255][MYSQL][]
SHOW INDEX FROM `ls_user`

[2025-08-24 20:09:16.542][腾讯_上门按摩][004253][MYSQL][]
SHOW CREATE TABLE `ls_user` 

[2025-08-24 20:09:16.558][腾讯_上门按摩][004253][MYSQL][]
SHOW FULL COLUMNS FROM `ls_user`

[2025-08-25 09:18:50.541][本地Mysql8][000571][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 09:18:50.554][本地Mysql8][000571][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 09:19:01.927][本地Mysql8][000571][MYSQL][]
SELECT * FROM `izhule`.`sys_user` LIMIT 0,1000

[2025-08-25 09:19:01.929][本地Mysql8][000571][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_user'

[2025-08-25 09:19:01.94][本地Mysql8][000573][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_user`

[2025-08-25 09:19:01.944][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_user`

[2025-08-25 09:19:01.946][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-25 09:19:01.948][本地Mysql8][000571][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-25 14:06:19.188][本地Mysql8][000571][MYSQL][]
UPDATE `izhule`.`sys_user` SET `nick_name` = 'hello' WHERE `user_id` = 100

[2025-08-25 14:06:19.193][本地Mysql8][000571][MYSQL][]
SELECT * FROM `izhule`.`sys_user` WHERE `user_id` = 100

[2025-08-25 14:06:27.866][本地Mysql8][000571][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_like'

[2025-08-25 14:06:27.869][本地Mysql8][000571][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-25 14:06:27.87][本地Mysql8][000571][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-25 14:06:27.874][本地Mysql8][000571][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-25 14:06:27.882][本地Mysql8][000571][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_like' ORDER BY event_object_table

[2025-08-25 14:06:27.884][本地Mysql8][000571][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 14:06:28.078][本地Mysql8][000571][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 14:06:37.534][本地Mysql8][000571][MYSQL][]
ALTER TABLE `izhule`.`note_like` 
MODIFY COLUMN `note_id` varchar(36) NOT NULL COMMENT '笔记ID' AFTER `id`

[2025-08-25 14:06:37.592][本地Mysql8][000571][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 14:06:37.599][本地Mysql8][000571][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_like'

[2025-08-25 14:06:37.601][本地Mysql8][000571][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-25 14:06:37.602][本地Mysql8][000571][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-25 14:06:37.605][本地Mysql8][000571][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-25 14:06:37.607][本地Mysql8][000571][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_like' ORDER BY event_object_table

[2025-08-25 14:06:37.609][本地Mysql8][000571][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 14:06:55.581][本地Mysql8][000571][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 14:06:55.587][本地Mysql8][000571][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_like'

[2025-08-25 14:06:55.589][本地Mysql8][000571][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-25 14:06:55.59][本地Mysql8][000571][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-25 14:06:55.592][本地Mysql8][000571][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-25 14:06:55.595][本地Mysql8][000571][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_like' ORDER BY event_object_table

[2025-08-25 14:06:55.597][本地Mysql8][000571][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 14:24:44.06][本地Mysql8][000571][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 14:24:44.121][本地Mysql8][000571][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 14:24:44.127][本地Mysql8][000571][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-25 14:24:47.572][本地Mysql8][000571][MYSQL][]
SET PROFILING = 1

[2025-08-25 14:24:47.572][本地Mysql8][000571][MYSQL][]
SHOW STATUS

[2025-08-25 14:24:47.581][本地Mysql8][000571][MYSQL][]
SHOW STATUS

[2025-08-25 14:24:47.59][本地Mysql8][000571][MYSQL][]
ALTER TABLE `note` ADD COLUMN `collect_count` int NULL DEFAULT 0 COMMENT '收藏数' AFTER `view_count`

[2025-08-25 14:24:47.609][本地Mysql8][000571][MYSQL][]
SHOW STATUS

[2025-08-25 14:24:47.619][本地Mysql8][000571][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-25 14:24:47.62][本地Mysql8][000571][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.001056*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=2 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-25 15:02:55.773][本地Mysql8][000571][MYSQL][]
SET PROFILING = 1

[2025-08-25 15:02:55.775][本地Mysql8][000571][MYSQL][]
SHOW STATUS

[2025-08-25 15:02:55.786][本地Mysql8][000571][MYSQL][]
SHOW STATUS

[2025-08-25 15:02:55.797][本地Mysql8][000571][MYSQL][]
-- 笔记评论点赞表
DROP TABLE IF EXISTS `note_comment_like`

[2025-08-25 15:02:55.802][本地Mysql8][000571][MYSQL][]
CREATE TABLE `note_comment_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '点赞记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `type` tinyint NOT NULL COMMENT '类型(1点赞 2点踩)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_user_comment` (`user_id`, `comment_id`),
  KEY `idx_comment_id` (`comment_id`)
) ENGINE=InnoDB COMMENT='笔记评论点赞表'

[2025-08-25 15:02:55.832][本地Mysql8][000571][MYSQL][]
SHOW STATUS

[2025-08-25 15:02:55.841][本地Mysql8][000571][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-25 15:02:55.843][本地Mysql8][000571][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.001056*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=2 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-25 15:05:28.817][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:05:28.825][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:05:36.027][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment\_like'

[2025-08-25 15:05:36.029][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-25 15:05:36.032][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-25 15:05:36.034][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-25 15:05:36.043][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment_like' ORDER BY event_object_table

[2025-08-25 15:05:36.045][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:05:36.272][本地Mysql8][000572][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 15:05:44.516][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment\_like'

[2025-08-25 15:05:44.519][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-25 15:05:44.521][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-25 15:05:44.523][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-25 15:05:44.534][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment_like' ORDER BY event_object_table

[2025-08-25 15:05:44.536][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:05:44.757][本地Mysql8][000572][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 15:06:00.111][本地Mysql8][000572][MYSQL][]
ALTER TABLE `izhule`.`fun_comment_like` 
MODIFY COLUMN `comment_id` varchar(36) NOT NULL COMMENT '评论ID' AFTER `user_id`

[2025-08-25 15:06:00.173][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:06:00.18][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment\_like'

[2025-08-25 15:06:00.182][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-25 15:06:00.184][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-25 15:06:00.186][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-25 15:06:00.19][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment_like' ORDER BY event_object_table

[2025-08-25 15:06:00.191][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:06:19.368][本地Mysql8][000572][MYSQL][]
ALTER TABLE `izhule`.`note_comment_like` 
MODIFY COLUMN `id` varchar(36) NOT NULL COMMENT '点赞记录ID' FIRST,
MODIFY COLUMN `comment_id` varchar(36) NOT NULL COMMENT '评论ID' AFTER `user_id`

[2025-08-25 15:06:19.43][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:06:19.436][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment\_like'

[2025-08-25 15:06:19.439][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-25 15:06:19.441][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-25 15:06:19.444][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-25 15:06:19.447][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment_like' ORDER BY event_object_table

[2025-08-25 15:06:19.448][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:06:48.768][本地Mysql8][000572][MYSQL][]
ALTER TABLE `izhule`.`note_comment_like` 
MODIFY COLUMN `type` varchar(2) NOT NULL DEFAULT "1" COMMENT '类型(1点赞 2点踩)' AFTER `comment_id`

[2025-08-25 15:06:48.828][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:06:48.835][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment\_like'

[2025-08-25 15:06:48.838][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-25 15:06:48.839][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-25 15:06:48.842][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-25 15:06:48.844][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment_like' ORDER BY event_object_table

[2025-08-25 15:06:48.846][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:07:00.588][本地Mysql8][000572][MYSQL][]
ALTER TABLE `izhule`.`fun_comment_like` 
MODIFY COLUMN `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT "1" COMMENT '类型(1点赞 2点踩)' AFTER `comment_id`

[2025-08-25 15:07:00.599][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:07:00.605][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment\_like'

[2025-08-25 15:07:00.608][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-25 15:07:00.61][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-25 15:07:00.613][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-25 15:07:00.615][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment_like' ORDER BY event_object_table

[2025-08-25 15:07:00.617][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:07:32.401][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:07:32.409][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:07:35.795][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:07:35.814][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_BLOB_TRIGGERS` 

[2025-08-25 15:07:35.815][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_CALENDARS` 

[2025-08-25 15:07:35.816][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_CRON_TRIGGERS` 

[2025-08-25 15:07:35.817][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_FIRED_TRIGGERS` 

[2025-08-25 15:07:35.819][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_JOB_DETAILS` 

[2025-08-25 15:07:35.82][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_LOCKS` 

[2025-08-25 15:07:35.821][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` 

[2025-08-25 15:07:35.822][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_SCHEDULER_STATE` 

[2025-08-25 15:07:35.823][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` 

[2025-08-25 15:07:35.824][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` 

[2025-08-25 15:07:35.826][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_TRIGGERS` 

[2025-08-25 15:07:35.827][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `agent` 

[2025-08-25 15:07:35.828][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `agent_application` 

[2025-08-25 15:07:35.829][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `agent_commission_record` 

[2025-08-25 15:07:35.831][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `asset_exchange` 

[2025-08-25 15:07:35.832][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `banner` 

[2025-08-25 15:07:35.833][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `city` 

[2025-08-25 15:07:35.834][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `comment_like` 

[2025-08-25 15:07:35.835][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `flyway_schema_history` 

[2025-08-25 15:07:35.836][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun` 

[2025-08-25 15:07:35.837][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_cart` 

[2025-08-25 15:07:35.838][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-25 15:07:35.84][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-25 15:07:35.841][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_merchant` 

[2025-08-25 15:07:35.842][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_order` 

[2025-08-25 15:07:35.843][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_order_redemption` 

[2025-08-25 15:07:35.844][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_rule` 

[2025-08-25 15:07:35.845][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `gen_table` 

[2025-08-25 15:07:35.847][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `gen_table_column` 

[2025-08-25 15:07:35.848][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods` 

[2025-08-25 15:07:35.849][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_category` 

[2025-08-25 15:07:35.85][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-25 15:07:35.852][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_tag` 

[2025-08-25 15:07:35.853][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_tag_relation` 

[2025-08-25 15:07:35.854][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `medal` 

[2025-08-25 15:07:35.855][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant` 

[2025-08-25 15:07:35.857][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_category` 

[2025-08-25 15:07:35.858][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_category_relation` 

[2025-08-25 15:07:35.859][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-25 15:07:35.86][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_comment_like` 

[2025-08-25 15:07:35.862][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_feature` 

[2025-08-25 15:07:35.863][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_tag` 

[2025-08-25 15:07:35.864][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note` 

[2025-08-25 15:07:35.866][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_collect` 

[2025-08-25 15:07:35.867][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-25 15:07:35.868][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-25 15:07:35.869][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-25 15:07:35.87][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_merchant` 

[2025-08-25 15:07:35.871][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_topic` 

[2025-08-25 15:07:35.872][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_useful` 

[2025-08-25 15:07:35.873][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `poster` 

[2025-08-25 15:07:35.875][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `poster_template` 

[2025-08-25 15:07:35.876][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `product` 

[2025-08-25 15:07:35.877][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `product_category` 

[2025-08-25 15:07:35.878][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `recommend_strategy` 

[2025-08-25 15:07:35.879][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `recommend_strategy_tag` 

[2025-08-25 15:07:35.881][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_area` 

[2025-08-25 15:07:35.882][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_config` 

[2025-08-25 15:07:35.883][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_dept` 

[2025-08-25 15:07:35.884][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_dict_data` 

[2025-08-25 15:07:35.886][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_dict_type` 

[2025-08-25 15:07:35.887][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_job` 

[2025-08-25 15:07:35.888][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_job_log` 

[2025-08-25 15:07:35.889][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_logininfor` 

[2025-08-25 15:07:35.891][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_menu` 

[2025-08-25 15:07:35.892][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_notice` 

[2025-08-25 15:07:35.893][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_oper_log` 

[2025-08-25 15:07:35.895][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_post` 

[2025-08-25 15:07:35.896][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_role` 

[2025-08-25 15:07:35.897][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_role_dept` 

[2025-08-25 15:07:35.898][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_role_menu` 

[2025-08-25 15:07:35.902][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_user` 

[2025-08-25 15:07:35.903][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_user_post` 

[2025-08-25 15:07:35.905][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_user_role` 

[2025-08-25 15:07:35.906][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `task` 

[2025-08-25 15:07:35.907][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_asset` 

[2025-08-25 15:07:35.908][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_follow` 

[2025-08-25 15:07:35.91][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_level` 

[2025-08-25 15:07:35.911][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_medal` 

[2025-08-25 15:07:35.912][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_recharge_record` 

[2025-08-25 15:07:35.914][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_task_completion` 

[2025-08-25 15:07:35.915][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_vip` 

[2025-08-25 15:07:35.931][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-25 15:07:35.934][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CALENDARS`

[2025-08-25 15:07:35.936][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CRON_TRIGGERS`

[2025-08-25 15:07:35.939][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-25 15:07:35.943][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_JOB_DETAILS`

[2025-08-25 15:07:35.946][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_LOCKS`

[2025-08-25 15:07:35.949][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-25 15:07:35.951][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SCHEDULER_STATE`

[2025-08-25 15:07:35.953][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-25 15:07:35.956][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-25 15:07:35.96][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_TRIGGERS`

[2025-08-25 15:07:35.964][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `agent`

[2025-08-25 15:07:35.97][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `agent_application`

[2025-08-25 15:07:35.976][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `agent_commission_record`

[2025-08-25 15:07:35.98][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `asset_exchange`

[2025-08-25 15:07:35.983][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `banner`

[2025-08-25 15:07:35.987][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `city`

[2025-08-25 15:07:35.99][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `comment_like`

[2025-08-25 15:07:35.993][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `flyway_schema_history`

[2025-08-25 15:07:35.996][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun`

[2025-08-25 15:07:36.001][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_cart`

[2025-08-25 15:07:36.004][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-25 15:07:36.008][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-25 15:07:36.011][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_merchant`

[2025-08-25 15:07:36.014][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order`

[2025-08-25 15:07:36.018][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order_redemption`

[2025-08-25 15:07:36.02][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_rule`

[2025-08-25 15:07:36.024][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table`

[2025-08-25 15:07:36.03][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table_column`

[2025-08-25 15:07:36.036][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods`

[2025-08-25 15:07:36.04][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_category`

[2025-08-25 15:07:36.043][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-25 15:07:36.046][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag`

[2025-08-25 15:07:36.048][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag_relation`

[2025-08-25 15:07:36.051][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `medal`

[2025-08-25 15:07:36.054][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant`

[2025-08-25 15:07:36.068][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category`

[2025-08-25 15:07:36.071][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category_relation`

[2025-08-25 15:07:36.073][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-25 15:07:36.077][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment_like`

[2025-08-25 15:07:36.08][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_feature`

[2025-08-25 15:07:36.082][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_tag`

[2025-08-25 15:07:36.085][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note`

[2025-08-25 15:07:36.089][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_collect`

[2025-08-25 15:07:36.091][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-25 15:07:36.094][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-25 15:07:36.097][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-25 15:07:36.099][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_merchant`

[2025-08-25 15:07:36.101][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_topic`

[2025-08-25 15:07:36.104][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_useful`

[2025-08-25 15:07:36.106][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `poster`

[2025-08-25 15:07:36.109][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `poster_template`

[2025-08-25 15:07:36.112][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `product`

[2025-08-25 15:07:36.116][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `product_category`

[2025-08-25 15:07:36.119][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy`

[2025-08-25 15:07:36.123][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy_tag`

[2025-08-25 15:07:36.125][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_area`

[2025-08-25 15:07:36.128][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_config`

[2025-08-25 15:07:36.132][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dept`

[2025-08-25 15:07:36.135][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_data`

[2025-08-25 15:07:36.139][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_type`

[2025-08-25 15:07:36.142][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job`

[2025-08-25 15:07:36.146][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job_log`

[2025-08-25 15:07:36.149][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_logininfor`

[2025-08-25 15:07:36.153][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-25 15:07:36.158][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_notice`

[2025-08-25 15:07:36.161][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_oper_log`

[2025-08-25 15:07:36.165][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_post`

[2025-08-25 15:07:36.169][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role`

[2025-08-25 15:07:36.172][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_dept`

[2025-08-25 15:07:36.175][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_menu`

[2025-08-25 15:07:36.177][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-25 15:07:36.182][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_post`

[2025-08-25 15:07:36.184][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_role`

[2025-08-25 15:07:36.186][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `task`

[2025-08-25 15:07:36.19][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_asset`

[2025-08-25 15:07:36.193][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_follow`

[2025-08-25 15:07:36.196][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_level`

[2025-08-25 15:07:36.199][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_medal`

[2025-08-25 15:07:36.201][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_recharge_record`

[2025-08-25 15:07:36.204][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_task_completion`

[2025-08-25 15:07:36.207][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_vip`

[2025-08-25 15:07:36.218][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-25 15:07:36.227][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_CALENDARS`

[2025-08-25 15:07:36.234][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_CRON_TRIGGERS`

[2025-08-25 15:07:36.242][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-25 15:07:36.25][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_JOB_DETAILS`

[2025-08-25 15:07:36.258][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_LOCKS`

[2025-08-25 15:07:36.265][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-25 15:07:36.271][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_SCHEDULER_STATE`

[2025-08-25 15:07:36.279][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-25 15:07:36.291][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-25 15:07:36.302][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_TRIGGERS`

[2025-08-25 15:07:36.315][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `agent`

[2025-08-25 15:07:36.336][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `agent_application`

[2025-08-25 15:07:36.352][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `agent_commission_record`

[2025-08-25 15:07:36.371][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `asset_exchange`

[2025-08-25 15:07:36.382][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `banner`

[2025-08-25 15:07:36.384][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `city`

[2025-08-25 15:07:36.394][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `comment_like`

[2025-08-25 15:07:36.403][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `flyway_schema_history`

[2025-08-25 15:07:36.41][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun`

[2025-08-25 15:07:36.418][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_cart`

[2025-08-25 15:07:36.436][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-25 15:07:36.439][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-25 15:07:36.441][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_merchant`

[2025-08-25 15:07:36.459][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_order`

[2025-08-25 15:07:36.471][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_order_redemption`

[2025-08-25 15:07:36.483][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_rule`

[2025-08-25 15:07:36.497][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `gen_table`

[2025-08-25 15:07:36.502][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `gen_table_column`

[2025-08-25 15:07:36.506][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods`

[2025-08-25 15:07:36.52][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_category`

[2025-08-25 15:07:36.527][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-25 15:07:36.53][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_tag`

[2025-08-25 15:07:36.537][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_tag_relation`

[2025-08-25 15:07:36.545][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `medal`

[2025-08-25 15:07:36.554][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant`

[2025-08-25 15:07:36.584][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_category`

[2025-08-25 15:07:36.59][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_category_relation`

[2025-08-25 15:07:36.599][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-25 15:07:36.602][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_comment_like`

[2025-08-25 15:07:36.615][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_feature`

[2025-08-25 15:07:36.623][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_tag`

[2025-08-25 15:07:36.631][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note`

[2025-08-25 15:07:36.634][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_collect`

[2025-08-25 15:07:36.647][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-25 15:07:36.65][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-25 15:07:36.652][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-25 15:07:36.654][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_merchant`

[2025-08-25 15:07:36.664][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_topic`

[2025-08-25 15:07:36.675][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_useful`

[2025-08-25 15:07:36.688][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `poster`

[2025-08-25 15:07:36.698][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `poster_template`

[2025-08-25 15:07:36.706][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `product`

[2025-08-25 15:07:36.722][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `product_category`

[2025-08-25 15:07:36.726][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `recommend_strategy`

[2025-08-25 15:07:36.733][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `recommend_strategy_tag`

[2025-08-25 15:07:36.747][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_area`

[2025-08-25 15:07:36.753][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_config`

[2025-08-25 15:07:36.759][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_dept`

[2025-08-25 15:07:36.765][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_dict_data`

[2025-08-25 15:07:36.77][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_dict_type`

[2025-08-25 15:07:36.779][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_job`

[2025-08-25 15:07:36.79][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_job_log`

[2025-08-25 15:07:36.794][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_logininfor`

[2025-08-25 15:07:36.802][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-25 15:07:36.806][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_notice`

[2025-08-25 15:07:36.811][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_oper_log`

[2025-08-25 15:07:36.821][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_post`

[2025-08-25 15:07:36.825][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_role`

[2025-08-25 15:07:36.83][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_role_dept`

[2025-08-25 15:07:36.835][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_role_menu`

[2025-08-25 15:07:36.842][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-25 15:07:36.844][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_user_post`

[2025-08-25 15:07:36.85][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_user_role`

[2025-08-25 15:07:36.857][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `task`

[2025-08-25 15:07:36.87][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_asset`

[2025-08-25 15:07:36.882][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_follow`

[2025-08-25 15:07:36.894][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_level`

[2025-08-25 15:07:36.901][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_medal`

[2025-08-25 15:07:36.91][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_recharge_record`

[2025-08-25 15:07:36.918][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_task_completion`

[2025-08-25 15:07:36.932][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_vip`

[2025-08-25 15:07:36.962][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' ORDER BY event_object_table

[2025-08-25 15:07:37.069][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-25 15:08:27.926][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:08:27.938][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:08:35.302][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment'

[2025-08-25 15:08:35.305][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-25 15:08:35.307][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-25 15:08:35.31][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-25 15:08:35.313][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment' ORDER BY event_object_table

[2025-08-25 15:08:35.314][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:08:35.555][本地Mysql8][000572][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 15:08:47.829][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:08:47.847][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_BLOB_TRIGGERS` 

[2025-08-25 15:08:47.848][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_CALENDARS` 

[2025-08-25 15:08:47.849][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_CRON_TRIGGERS` 

[2025-08-25 15:08:47.851][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_FIRED_TRIGGERS` 

[2025-08-25 15:08:47.852][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_JOB_DETAILS` 

[2025-08-25 15:08:47.853][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_LOCKS` 

[2025-08-25 15:08:47.854][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` 

[2025-08-25 15:08:47.855][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_SCHEDULER_STATE` 

[2025-08-25 15:08:47.857][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` 

[2025-08-25 15:08:47.858][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` 

[2025-08-25 15:08:47.859][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `QRTZ_TRIGGERS` 

[2025-08-25 15:08:47.86][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `agent` 

[2025-08-25 15:08:47.862][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `agent_application` 

[2025-08-25 15:08:47.863][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `agent_commission_record` 

[2025-08-25 15:08:47.864][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `asset_exchange` 

[2025-08-25 15:08:47.865][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `banner` 

[2025-08-25 15:08:47.866][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `city` 

[2025-08-25 15:08:47.867][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `comment_like` 

[2025-08-25 15:08:47.868][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `flyway_schema_history` 

[2025-08-25 15:08:47.869][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun` 

[2025-08-25 15:08:47.87][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_cart` 

[2025-08-25 15:08:47.871][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-25 15:08:47.872][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-25 15:08:47.873][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_merchant` 

[2025-08-25 15:08:47.874][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_order` 

[2025-08-25 15:08:47.875][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_order_redemption` 

[2025-08-25 15:08:47.876][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_rule` 

[2025-08-25 15:08:47.877][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `gen_table` 

[2025-08-25 15:08:47.879][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `gen_table_column` 

[2025-08-25 15:08:47.88][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods` 

[2025-08-25 15:08:47.881][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_category` 

[2025-08-25 15:08:47.882][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-25 15:08:47.883][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_tag` 

[2025-08-25 15:08:47.885][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `goods_tag_relation` 

[2025-08-25 15:08:47.886][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `medal` 

[2025-08-25 15:08:47.887][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant` 

[2025-08-25 15:08:47.888][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_category` 

[2025-08-25 15:08:47.889][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_category_relation` 

[2025-08-25 15:08:47.89][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-25 15:08:47.892][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_comment_like` 

[2025-08-25 15:08:47.893][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_feature` 

[2025-08-25 15:08:47.894][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `merchant_tag` 

[2025-08-25 15:08:47.895][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note` 

[2025-08-25 15:08:47.896][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_collect` 

[2025-08-25 15:08:47.897][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-25 15:08:47.898][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-25 15:08:47.899][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-25 15:08:47.9][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_merchant` 

[2025-08-25 15:08:47.901][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_topic` 

[2025-08-25 15:08:47.902][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `note_useful` 

[2025-08-25 15:08:47.903][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `poster` 

[2025-08-25 15:08:47.904][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `poster_template` 

[2025-08-25 15:08:47.905][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `product` 

[2025-08-25 15:08:47.906][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `product_category` 

[2025-08-25 15:08:47.907][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `recommend_strategy` 

[2025-08-25 15:08:47.908][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `recommend_strategy_tag` 

[2025-08-25 15:08:47.909][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_area` 

[2025-08-25 15:08:47.91][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_config` 

[2025-08-25 15:08:47.912][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_dept` 

[2025-08-25 15:08:47.913][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_dict_data` 

[2025-08-25 15:08:47.914][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_dict_type` 

[2025-08-25 15:08:47.915][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_job` 

[2025-08-25 15:08:47.916][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_job_log` 

[2025-08-25 15:08:47.917][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_logininfor` 

[2025-08-25 15:08:47.919][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_menu` 

[2025-08-25 15:08:47.92][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_notice` 

[2025-08-25 15:08:47.921][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_oper_log` 

[2025-08-25 15:08:47.922][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_post` 

[2025-08-25 15:08:47.923][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_role` 

[2025-08-25 15:08:47.924][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_role_dept` 

[2025-08-25 15:08:47.925][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_role_menu` 

[2025-08-25 15:08:47.926][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_user` 

[2025-08-25 15:08:47.928][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_user_post` 

[2025-08-25 15:08:47.929][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `sys_user_role` 

[2025-08-25 15:08:47.931][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `task` 

[2025-08-25 15:08:47.932][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_asset` 

[2025-08-25 15:08:47.933][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_follow` 

[2025-08-25 15:08:47.934][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_level` 

[2025-08-25 15:08:47.935][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_medal` 

[2025-08-25 15:08:47.936][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_recharge_record` 

[2025-08-25 15:08:47.938][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_task_completion` 

[2025-08-25 15:08:47.939][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `user_vip` 

[2025-08-25 15:08:47.968][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-25 15:08:47.971][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CALENDARS`

[2025-08-25 15:08:47.973][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CRON_TRIGGERS`

[2025-08-25 15:08:47.976][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-25 15:08:47.981][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_JOB_DETAILS`

[2025-08-25 15:08:47.985][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_LOCKS`

[2025-08-25 15:08:47.987][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-25 15:08:47.989][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SCHEDULER_STATE`

[2025-08-25 15:08:47.991][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-25 15:08:47.994][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-25 15:08:47.997][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_TRIGGERS`

[2025-08-25 15:08:48.001][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `agent`

[2025-08-25 15:08:48.007][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `agent_application`

[2025-08-25 15:08:48.012][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `agent_commission_record`

[2025-08-25 15:08:48.017][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `asset_exchange`

[2025-08-25 15:08:48.02][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `banner`

[2025-08-25 15:08:48.023][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `city`

[2025-08-25 15:08:48.026][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `comment_like`

[2025-08-25 15:08:48.029][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `flyway_schema_history`

[2025-08-25 15:08:48.032][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun`

[2025-08-25 15:08:48.037][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_cart`

[2025-08-25 15:08:48.04][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-25 15:08:48.043][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-25 15:08:48.046][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_merchant`

[2025-08-25 15:08:48.049][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order`

[2025-08-25 15:08:48.052][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order_redemption`

[2025-08-25 15:08:48.055][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_rule`

[2025-08-25 15:08:48.058][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table`

[2025-08-25 15:08:48.063][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table_column`

[2025-08-25 15:08:48.07][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods`

[2025-08-25 15:08:48.075][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_category`

[2025-08-25 15:08:48.078][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-25 15:08:48.082][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag`

[2025-08-25 15:08:48.084][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag_relation`

[2025-08-25 15:08:48.086][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `medal`

[2025-08-25 15:08:48.09][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant`

[2025-08-25 15:08:48.103][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category`

[2025-08-25 15:08:48.106][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category_relation`

[2025-08-25 15:08:48.108][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-25 15:08:48.111][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment_like`

[2025-08-25 15:08:48.114][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_feature`

[2025-08-25 15:08:48.116][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_tag`

[2025-08-25 15:08:48.118][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note`

[2025-08-25 15:08:48.122][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_collect`

[2025-08-25 15:08:48.124][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-25 15:08:48.127][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-25 15:08:48.13][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-25 15:08:48.132][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_merchant`

[2025-08-25 15:08:48.134][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_topic`

[2025-08-25 15:08:48.136][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `note_useful`

[2025-08-25 15:08:48.138][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `poster`

[2025-08-25 15:08:48.141][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `poster_template`

[2025-08-25 15:08:48.143][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `product`

[2025-08-25 15:08:48.148][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `product_category`

[2025-08-25 15:08:48.151][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy`

[2025-08-25 15:08:48.154][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy_tag`

[2025-08-25 15:08:48.156][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_area`

[2025-08-25 15:08:48.159][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_config`

[2025-08-25 15:08:48.162][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dept`

[2025-08-25 15:08:48.166][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_data`

[2025-08-25 15:08:48.169][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_type`

[2025-08-25 15:08:48.172][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job`

[2025-08-25 15:08:48.175][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job_log`

[2025-08-25 15:08:48.178][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_logininfor`

[2025-08-25 15:08:48.182][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-25 15:08:48.186][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_notice`

[2025-08-25 15:08:48.189][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_oper_log`

[2025-08-25 15:08:48.193][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_post`

[2025-08-25 15:08:48.196][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role`

[2025-08-25 15:08:48.2][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_dept`

[2025-08-25 15:08:48.201][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_menu`

[2025-08-25 15:08:48.203][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-25 15:08:48.208][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_post`

[2025-08-25 15:08:48.21][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_role`

[2025-08-25 15:08:48.212][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `task`

[2025-08-25 15:08:48.215][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_asset`

[2025-08-25 15:08:48.218][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_follow`

[2025-08-25 15:08:48.221][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_level`

[2025-08-25 15:08:48.224][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_medal`

[2025-08-25 15:08:48.226][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_recharge_record`

[2025-08-25 15:08:48.229][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_task_completion`

[2025-08-25 15:08:48.231][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `user_vip`

[2025-08-25 15:08:48.241][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-25 15:08:48.243][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_CALENDARS`

[2025-08-25 15:08:48.245][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_CRON_TRIGGERS`

[2025-08-25 15:08:48.247][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-25 15:08:48.249][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_JOB_DETAILS`

[2025-08-25 15:08:48.251][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_LOCKS`

[2025-08-25 15:08:48.252][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-25 15:08:48.254][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_SCHEDULER_STATE`

[2025-08-25 15:08:48.256][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-25 15:08:48.258][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-25 15:08:48.26][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `QRTZ_TRIGGERS`

[2025-08-25 15:08:48.262][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `agent`

[2025-08-25 15:08:48.265][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `agent_application`

[2025-08-25 15:08:48.268][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `agent_commission_record`

[2025-08-25 15:08:48.271][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `asset_exchange`

[2025-08-25 15:08:48.273][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `banner`

[2025-08-25 15:08:48.275][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `city`

[2025-08-25 15:08:48.277][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `comment_like`

[2025-08-25 15:08:48.279][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `flyway_schema_history`

[2025-08-25 15:08:48.281][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun`

[2025-08-25 15:08:48.283][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_cart`

[2025-08-25 15:08:48.285][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-25 15:08:48.287][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-25 15:08:48.289][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_merchant`

[2025-08-25 15:08:48.292][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_order`

[2025-08-25 15:08:48.294][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_order_redemption`

[2025-08-25 15:08:48.296][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_rule`

[2025-08-25 15:08:48.299][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `gen_table`

[2025-08-25 15:08:48.3][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `gen_table_column`

[2025-08-25 15:08:48.302][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods`

[2025-08-25 15:08:48.304][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_category`

[2025-08-25 15:08:48.306][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-25 15:08:48.308][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_tag`

[2025-08-25 15:08:48.31][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `goods_tag_relation`

[2025-08-25 15:08:48.312][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `medal`

[2025-08-25 15:08:48.314][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant`

[2025-08-25 15:08:48.318][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_category`

[2025-08-25 15:08:48.32][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_category_relation`

[2025-08-25 15:08:48.322][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-25 15:08:48.324][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_comment_like`

[2025-08-25 15:08:48.326][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_feature`

[2025-08-25 15:08:48.328][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `merchant_tag`

[2025-08-25 15:08:48.33][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note`

[2025-08-25 15:08:48.333][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_collect`

[2025-08-25 15:08:48.335][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-25 15:08:48.337][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-25 15:08:48.339][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-25 15:08:48.341][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_merchant`

[2025-08-25 15:08:48.343][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_topic`

[2025-08-25 15:08:48.345][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `note_useful`

[2025-08-25 15:08:48.347][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `poster`

[2025-08-25 15:08:48.349][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `poster_template`

[2025-08-25 15:08:48.35][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `product`

[2025-08-25 15:08:48.353][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `product_category`

[2025-08-25 15:08:48.355][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `recommend_strategy`

[2025-08-25 15:08:48.356][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `recommend_strategy_tag`

[2025-08-25 15:08:48.358][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_area`

[2025-08-25 15:08:48.36][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_config`

[2025-08-25 15:08:48.362][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_dept`

[2025-08-25 15:08:48.364][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_dict_data`

[2025-08-25 15:08:48.365][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_dict_type`

[2025-08-25 15:08:48.367][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_job`

[2025-08-25 15:08:48.369][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_job_log`

[2025-08-25 15:08:48.371][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_logininfor`

[2025-08-25 15:08:48.373][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-25 15:08:48.375][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_notice`

[2025-08-25 15:08:48.377][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_oper_log`

[2025-08-25 15:08:48.38][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_post`

[2025-08-25 15:08:48.381][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_role`

[2025-08-25 15:08:48.383][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_role_dept`

[2025-08-25 15:08:48.385][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_role_menu`

[2025-08-25 15:08:48.387][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-25 15:08:48.389][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_user_post`

[2025-08-25 15:08:48.39][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `sys_user_role`

[2025-08-25 15:08:48.392][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `task`

[2025-08-25 15:08:48.395][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_asset`

[2025-08-25 15:08:48.397][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_follow`

[2025-08-25 15:08:48.399][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_level`

[2025-08-25 15:08:48.401][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_medal`

[2025-08-25 15:08:48.403][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_recharge_record`

[2025-08-25 15:08:48.405][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_task_completion`

[2025-08-25 15:08:48.408][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `user_vip`

[2025-08-25 15:08:48.43][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' ORDER BY event_object_table

[2025-08-25 15:08:48.517][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-25 15:09:15.591][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 15:09:15.601][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:09:19.105][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_order'

[2025-08-25 15:09:19.107][本地Mysql8][000572][MYSQL][]
SHOW CREATE TABLE `fun_order` 

[2025-08-25 15:09:19.108][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order`

[2025-08-25 15:09:19.112][本地Mysql8][000572][MYSQL][]
SHOW INDEX FROM `fun_order`

[2025-08-25 15:09:19.115][本地Mysql8][000572][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_order' ORDER BY event_object_table

[2025-08-25 15:09:19.116][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_order' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:19.362][本地Mysql8][000572][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 15:09:51.452][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 15:09:51.475][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-25 15:09:51.478][本地Mysql8][000573][MYSQL][]
SHOW PROCEDURE STATUS WHERE Db = 'izhule'

[2025-08-25 15:09:51.479][本地Mysql8][000573][MYSQL][]
SHOW FUNCTION STATUS WHERE Db = 'izhule'

[2025-08-25 15:09:51.481][本地Mysql8][000573][MYSQL][]
SELECT * FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'izhule' ORDER BY ROUTINE_NAME

[2025-08-25 15:09:51.484][本地Mysql8][000573][MYSQL][]
SELECT EVENT_CATALOG, EVENT_SCHEMA, EVENT_NAME, DEFINER, TIME_ZONE, EVENT_DEFINITION, EVENT_BODY, EVENT_TYPE, SQL_MODE, STATUS, EXECUTE_AT, INTERVAL_VALUE, INTERVAL_FIELD, STARTS, ENDS, ON_COMPLETION, CREATED, LAST_ALTERED, LAST_EXECUTED, ORIGINATOR, CHARACTER_SET_CLIENT, COLLATION_CONNECTION, DATABASE_COLLATION, EVENT_COMMENT FROM information_schema.EVENTS WHERE EVENT_SCHEMA = 'izhule' ORDER BY EVENT_NAME ASC

[2025-08-25 15:09:51.489][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_BLOB\_TRIGGERS'

[2025-08-25 15:09:51.491][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_BLOB_TRIGGERS` 

[2025-08-25 15:09:51.492][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-25 15:09:51.495][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-25 15:09:51.497][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_BLOB_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.499][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_BLOB_TRIGGERS' ORDER BY event_object_table

[2025-08-25 15:09:51.508][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_CALENDARS'

[2025-08-25 15:09:51.511][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_CALENDARS` 

[2025-08-25 15:09:51.512][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CALENDARS`

[2025-08-25 15:09:51.515][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_CALENDARS`

[2025-08-25 15:09:51.517][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_CALENDARS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.519][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_CALENDARS' ORDER BY event_object_table

[2025-08-25 15:09:51.524][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_CRON\_TRIGGERS'

[2025-08-25 15:09:51.527][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_CRON_TRIGGERS` 

[2025-08-25 15:09:51.528][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CRON_TRIGGERS`

[2025-08-25 15:09:51.53][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_CRON_TRIGGERS`

[2025-08-25 15:09:51.533][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_CRON_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.535][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_CRON_TRIGGERS' ORDER BY event_object_table

[2025-08-25 15:09:51.541][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_FIRED\_TRIGGERS'

[2025-08-25 15:09:51.544][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_FIRED_TRIGGERS` 

[2025-08-25 15:09:51.545][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-25 15:09:51.55][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-25 15:09:51.553][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_FIRED_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.555][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_FIRED_TRIGGERS' ORDER BY event_object_table

[2025-08-25 15:09:51.563][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_JOB\_DETAILS'

[2025-08-25 15:09:51.566][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_JOB_DETAILS` 

[2025-08-25 15:09:51.567][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_JOB_DETAILS`

[2025-08-25 15:09:51.57][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_JOB_DETAILS`

[2025-08-25 15:09:51.572][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_JOB_DETAILS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.574][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_JOB_DETAILS' ORDER BY event_object_table

[2025-08-25 15:09:51.584][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_LOCKS'

[2025-08-25 15:09:51.586][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_LOCKS` 

[2025-08-25 15:09:51.588][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_LOCKS`

[2025-08-25 15:09:51.59][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_LOCKS`

[2025-08-25 15:09:51.592][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_LOCKS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.594][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_LOCKS' ORDER BY event_object_table

[2025-08-25 15:09:51.6][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_PAUSED\_TRIGGER\_GRPS'

[2025-08-25 15:09:51.602][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` 

[2025-08-25 15:09:51.603][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-25 15:09:51.605][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-25 15:09:51.607][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_PAUSED_TRIGGER_GRPS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.609][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_PAUSED_TRIGGER_GRPS' ORDER BY event_object_table

[2025-08-25 15:09:51.614][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_SCHEDULER\_STATE'

[2025-08-25 15:09:51.617][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_SCHEDULER_STATE` 

[2025-08-25 15:09:51.618][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SCHEDULER_STATE`

[2025-08-25 15:09:51.62][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_SCHEDULER_STATE`

[2025-08-25 15:09:51.622][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_SCHEDULER_STATE' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.624][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_SCHEDULER_STATE' ORDER BY event_object_table

[2025-08-25 15:09:51.631][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_SIMPLE\_TRIGGERS'

[2025-08-25 15:09:51.634][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` 

[2025-08-25 15:09:51.635][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-25 15:09:51.637][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-25 15:09:51.64][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_SIMPLE_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.641][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_SIMPLE_TRIGGERS' ORDER BY event_object_table

[2025-08-25 15:09:51.649][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_SIMPROP\_TRIGGERS'

[2025-08-25 15:09:51.651][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` 

[2025-08-25 15:09:51.653][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-25 15:09:51.656][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-25 15:09:51.658][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_SIMPROP_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.66][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_SIMPROP_TRIGGERS' ORDER BY event_object_table

[2025-08-25 15:09:51.669][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_TRIGGERS'

[2025-08-25 15:09:51.671][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `QRTZ_TRIGGERS` 

[2025-08-25 15:09:51.672][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_TRIGGERS`

[2025-08-25 15:09:51.676][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `QRTZ_TRIGGERS`

[2025-08-25 15:09:51.679][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.681][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_TRIGGERS' ORDER BY event_object_table

[2025-08-25 15:09:51.691][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'agent'

[2025-08-25 15:09:51.694][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `agent` 

[2025-08-25 15:09:51.695][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `agent`

[2025-08-25 15:09:51.701][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `agent`

[2025-08-25 15:09:51.704][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'agent' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.706][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'agent' ORDER BY event_object_table

[2025-08-25 15:09:51.723][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'agent\_application'

[2025-08-25 15:09:51.725][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `agent_application` 

[2025-08-25 15:09:51.727][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `agent_application`

[2025-08-25 15:09:51.732][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `agent_application`

[2025-08-25 15:09:51.736][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'agent_application' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.737][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'agent_application' ORDER BY event_object_table

[2025-08-25 15:09:51.752][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'agent\_commission\_record'

[2025-08-25 15:09:51.754][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `agent_commission_record` 

[2025-08-25 15:09:51.756][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `agent_commission_record`

[2025-08-25 15:09:51.759][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `agent_commission_record`

[2025-08-25 15:09:51.762][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'agent_commission_record' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.764][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'agent_commission_record' ORDER BY event_object_table

[2025-08-25 15:09:51.775][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'asset\_exchange'

[2025-08-25 15:09:51.777][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `asset_exchange` 

[2025-08-25 15:09:51.778][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `asset_exchange`

[2025-08-25 15:09:51.783][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `asset_exchange`

[2025-08-25 15:09:51.786][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'asset_exchange' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.788][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'asset_exchange' ORDER BY event_object_table

[2025-08-25 15:09:51.796][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'banner'

[2025-08-25 15:09:51.799][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `banner` 

[2025-08-25 15:09:51.8][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `banner`

[2025-08-25 15:09:51.803][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `banner`

[2025-08-25 15:09:51.806][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'banner' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.807][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'banner' ORDER BY event_object_table

[2025-08-25 15:09:51.818][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'city'

[2025-08-25 15:09:51.821][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `city` 

[2025-08-25 15:09:51.822][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `city`

[2025-08-25 15:09:51.825][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `city`

[2025-08-25 15:09:51.828][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'city' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.83][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'city' ORDER BY event_object_table

[2025-08-25 15:09:51.838][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'comment\_like'

[2025-08-25 15:09:51.84][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `comment_like` 

[2025-08-25 15:09:51.841][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `comment_like`

[2025-08-25 15:09:51.844][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `comment_like`

[2025-08-25 15:09:51.846][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.848][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'comment_like' ORDER BY event_object_table

[2025-08-25 15:09:51.855][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'flyway\_schema\_history'

[2025-08-25 15:09:51.858][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `flyway_schema_history` 

[2025-08-25 15:09:51.859][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `flyway_schema_history`

[2025-08-25 15:09:51.862][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `flyway_schema_history`

[2025-08-25 15:09:51.864][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'flyway_schema_history' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.866][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'flyway_schema_history' ORDER BY event_object_table

[2025-08-25 15:09:51.874][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun'

[2025-08-25 15:09:51.876][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun` 

[2025-08-25 15:09:51.877][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun`

[2025-08-25 15:09:51.881][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun`

[2025-08-25 15:09:51.884][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.886][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun' ORDER BY event_object_table

[2025-08-25 15:09:51.898][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_cart'

[2025-08-25 15:09:51.9][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_cart` 

[2025-08-25 15:09:51.902][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_cart`

[2025-08-25 15:09:51.905][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_cart`

[2025-08-25 15:09:51.907][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_cart' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.909][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_cart' ORDER BY event_object_table

[2025-08-25 15:09:51.919][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment'

[2025-08-25 15:09:51.921][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-25 15:09:51.922][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-25 15:09:51.925][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-25 15:09:51.928][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.93][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment' ORDER BY event_object_table

[2025-08-25 15:09:51.938][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment\_like'

[2025-08-25 15:09:51.941][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-25 15:09:51.942][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-25 15:09:51.944][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-25 15:09:51.947][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.949][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment_like' ORDER BY event_object_table

[2025-08-25 15:09:51.955][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_merchant'

[2025-08-25 15:09:51.958][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_merchant` 

[2025-08-25 15:09:51.959][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_merchant`

[2025-08-25 15:09:51.962][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_merchant`

[2025-08-25 15:09:51.965][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_merchant' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.967][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_merchant' ORDER BY event_object_table

[2025-08-25 15:09:51.976][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_order'

[2025-08-25 15:09:51.978][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_order` 

[2025-08-25 15:09:51.98][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order`

[2025-08-25 15:09:51.984][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_order`

[2025-08-25 15:09:51.987][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_order' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:51.988][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_order' ORDER BY event_object_table

[2025-08-25 15:09:51.997][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_order\_redemption'

[2025-08-25 15:09:52.000][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_order_redemption` 

[2025-08-25 15:09:52.001][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order_redemption`

[2025-08-25 15:09:52.004][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_order_redemption`

[2025-08-25 15:09:52.006][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_order_redemption' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.008][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_order_redemption' ORDER BY event_object_table

[2025-08-25 15:09:52.015][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_rule'

[2025-08-25 15:09:52.018][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `fun_rule` 

[2025-08-25 15:09:52.019][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `fun_rule`

[2025-08-25 15:09:52.022][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `fun_rule`

[2025-08-25 15:09:52.025][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_rule' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.027][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_rule' ORDER BY event_object_table

[2025-08-25 15:09:52.035][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'gen\_table'

[2025-08-25 15:09:52.038][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `gen_table` 

[2025-08-25 15:09:52.039][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table`

[2025-08-25 15:09:52.044][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `gen_table`

[2025-08-25 15:09:52.046][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'gen_table' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.049][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'gen_table' ORDER BY event_object_table

[2025-08-25 15:09:52.06][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'gen\_table\_column'

[2025-08-25 15:09:52.063][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `gen_table_column` 

[2025-08-25 15:09:52.064][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table_column`

[2025-08-25 15:09:52.07][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `gen_table_column`

[2025-08-25 15:09:52.072][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'gen_table_column' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.074][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'gen_table_column' ORDER BY event_object_table

[2025-08-25 15:09:52.089][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'goods'

[2025-08-25 15:09:52.092][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `goods` 

[2025-08-25 15:09:52.093][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `goods`

[2025-08-25 15:09:52.097][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `goods`

[2025-08-25 15:09:52.1][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.102][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods' ORDER BY event_object_table

[2025-08-25 15:09:52.113][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_category'

[2025-08-25 15:09:52.116][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `goods_category` 

[2025-08-25 15:09:52.118][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `goods_category`

[2025-08-25 15:09:52.121][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `goods_category`

[2025-08-25 15:09:52.123][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.125][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_category' ORDER BY event_object_table

[2025-08-25 15:09:52.133][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_comment'

[2025-08-25 15:09:52.136][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-25 15:09:52.137][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-25 15:09:52.14][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-25 15:09:52.143][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.145][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_comment' ORDER BY event_object_table

[2025-08-25 15:09:52.154][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_tag'

[2025-08-25 15:09:52.157][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `goods_tag` 

[2025-08-25 15:09:52.158][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag`

[2025-08-25 15:09:52.161][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `goods_tag`

[2025-08-25 15:09:52.163][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_tag' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.165][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_tag' ORDER BY event_object_table

[2025-08-25 15:09:52.172][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_tag\_relation'

[2025-08-25 15:09:52.175][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `goods_tag_relation` 

[2025-08-25 15:09:52.176][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag_relation`

[2025-08-25 15:09:52.179][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `goods_tag_relation`

[2025-08-25 15:09:52.181][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_tag_relation' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.185][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_tag_relation' ORDER BY event_object_table

[2025-08-25 15:09:52.191][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'medal'

[2025-08-25 15:09:52.194][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `medal` 

[2025-08-25 15:09:52.195][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `medal`

[2025-08-25 15:09:52.199][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `medal`

[2025-08-25 15:09:52.201][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'medal' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.203][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'medal' ORDER BY event_object_table

[2025-08-25 15:09:52.212][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant'

[2025-08-25 15:09:52.214][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant` 

[2025-08-25 15:09:52.216][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant`

[2025-08-25 15:09:52.229][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant`

[2025-08-25 15:09:52.235][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.237][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant' ORDER BY event_object_table

[2025-08-25 15:09:52.272][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_category'

[2025-08-25 15:09:52.274][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant_category` 

[2025-08-25 15:09:52.276][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category`

[2025-08-25 15:09:52.278][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant_category`

[2025-08-25 15:09:52.281][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.283][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_category' ORDER BY event_object_table

[2025-08-25 15:09:52.29][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_category\_relation'

[2025-08-25 15:09:52.293][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant_category_relation` 

[2025-08-25 15:09:52.294][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category_relation`

[2025-08-25 15:09:52.296][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant_category_relation`

[2025-08-25 15:09:52.299][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_category_relation' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.301][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_category_relation' ORDER BY event_object_table

[2025-08-25 15:09:52.307][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment'

[2025-08-25 15:09:52.309][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-25 15:09:52.311][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-25 15:09:52.314][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-25 15:09:52.317][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.319][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment' ORDER BY event_object_table

[2025-08-25 15:09:52.327][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment\_like'

[2025-08-25 15:09:52.329][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant_comment_like` 

[2025-08-25 15:09:52.331][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment_like`

[2025-08-25 15:09:52.333][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant_comment_like`

[2025-08-25 15:09:52.336][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.337][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment_like' ORDER BY event_object_table

[2025-08-25 15:09:52.344][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_feature'

[2025-08-25 15:09:52.346][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant_feature` 

[2025-08-25 15:09:52.347][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_feature`

[2025-08-25 15:09:52.35][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant_feature`

[2025-08-25 15:09:52.352][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_feature' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.353][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_feature' ORDER BY event_object_table

[2025-08-25 15:09:52.359][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_tag'

[2025-08-25 15:09:52.361][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `merchant_tag` 

[2025-08-25 15:09:52.363][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_tag`

[2025-08-25 15:09:52.366][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `merchant_tag`

[2025-08-25 15:09:52.368][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_tag' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.37][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_tag' ORDER BY event_object_table

[2025-08-25 15:09:52.376][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note'

[2025-08-25 15:09:52.379][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note` 

[2025-08-25 15:09:52.38][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note`

[2025-08-25 15:09:52.385][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note`

[2025-08-25 15:09:52.388][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.39][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note' ORDER BY event_object_table

[2025-08-25 15:09:52.4][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_collect'

[2025-08-25 15:09:52.403][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_collect` 

[2025-08-25 15:09:52.404][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_collect`

[2025-08-25 15:09:52.406][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_collect`

[2025-08-25 15:09:52.409][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_collect' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.411][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_collect' ORDER BY event_object_table

[2025-08-25 15:09:52.418][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-25 15:09:52.421][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-25 15:09:52.422][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-25 15:09:52.425][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-25 15:09:52.427][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.429][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-25 15:09:52.438][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment\_like'

[2025-08-25 15:09:52.44][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-25 15:09:52.441][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-25 15:09:52.444][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-25 15:09:52.446][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.448][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment_like' ORDER BY event_object_table

[2025-08-25 15:09:52.455][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_like'

[2025-08-25 15:09:52.457][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-25 15:09:52.458][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-25 15:09:52.46][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-25 15:09:52.462][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.464][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_like' ORDER BY event_object_table

[2025-08-25 15:09:52.471][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_merchant'

[2025-08-25 15:09:52.473][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_merchant` 

[2025-08-25 15:09:52.475][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_merchant`

[2025-08-25 15:09:52.477][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_merchant`

[2025-08-25 15:09:52.479][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_merchant' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.48][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_merchant' ORDER BY event_object_table

[2025-08-25 15:09:52.486][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_topic'

[2025-08-25 15:09:52.488][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_topic` 

[2025-08-25 15:09:52.49][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_topic`

[2025-08-25 15:09:52.492][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_topic`

[2025-08-25 15:09:52.494][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_topic' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.496][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_topic' ORDER BY event_object_table

[2025-08-25 15:09:52.503][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_useful'

[2025-08-25 15:09:52.506][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `note_useful` 

[2025-08-25 15:09:52.507][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `note_useful`

[2025-08-25 15:09:52.509][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `note_useful`

[2025-08-25 15:09:52.512][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_useful' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.513][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_useful' ORDER BY event_object_table

[2025-08-25 15:09:52.52][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'poster'

[2025-08-25 15:09:52.523][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `poster` 

[2025-08-25 15:09:52.524][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `poster`

[2025-08-25 15:09:52.527][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `poster`

[2025-08-25 15:09:52.529][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'poster' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.531][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'poster' ORDER BY event_object_table

[2025-08-25 15:09:52.538][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'poster\_template'

[2025-08-25 15:09:52.54][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `poster_template` 

[2025-08-25 15:09:52.541][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `poster_template`

[2025-08-25 15:09:52.544][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `poster_template`

[2025-08-25 15:09:52.546][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'poster_template' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.548][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'poster_template' ORDER BY event_object_table

[2025-08-25 15:09:52.555][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'product'

[2025-08-25 15:09:52.558][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `product` 

[2025-08-25 15:09:52.559][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `product`

[2025-08-25 15:09:52.563][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `product`

[2025-08-25 15:09:52.565][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'product' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.567][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'product' ORDER BY event_object_table

[2025-08-25 15:09:52.577][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'product\_category'

[2025-08-25 15:09:52.579][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `product_category` 

[2025-08-25 15:09:52.58][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `product_category`

[2025-08-25 15:09:52.584][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `product_category`

[2025-08-25 15:09:52.587][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'product_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.588][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'product_category' ORDER BY event_object_table

[2025-08-25 15:09:52.596][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'recommend\_strategy'

[2025-08-25 15:09:52.599][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `recommend_strategy` 

[2025-08-25 15:09:52.6][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy`

[2025-08-25 15:09:52.604][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `recommend_strategy`

[2025-08-25 15:09:52.606][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'recommend_strategy' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.608][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'recommend_strategy' ORDER BY event_object_table

[2025-08-25 15:09:52.619][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'recommend\_strategy\_tag'

[2025-08-25 15:09:52.621][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `recommend_strategy_tag` 

[2025-08-25 15:09:52.623][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy_tag`

[2025-08-25 15:09:52.625][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `recommend_strategy_tag`

[2025-08-25 15:09:52.627][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'recommend_strategy_tag' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.629][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'recommend_strategy_tag' ORDER BY event_object_table

[2025-08-25 15:09:52.636][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_area'

[2025-08-25 15:09:52.638][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_area` 

[2025-08-25 15:09:52.639][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_area`

[2025-08-25 15:09:52.642][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_area`

[2025-08-25 15:09:52.644][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_area' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:52.646][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_area' ORDER BY event_object_table

[2025-08-25 15:09:53.072][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_config'

[2025-08-25 15:09:53.075][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_config` 

[2025-08-25 15:09:53.076][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_config`

[2025-08-25 15:09:53.08][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_config`

[2025-08-25 15:09:53.082][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_config' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.084][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_config' ORDER BY event_object_table

[2025-08-25 15:09:53.093][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_dept'

[2025-08-25 15:09:53.095][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_dept` 

[2025-08-25 15:09:53.096][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dept`

[2025-08-25 15:09:53.1][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_dept`

[2025-08-25 15:09:53.103][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_dept' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.104][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_dept' ORDER BY event_object_table

[2025-08-25 15:09:53.114][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_dict\_data'

[2025-08-25 15:09:53.117][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_dict_data` 

[2025-08-25 15:09:53.118][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_data`

[2025-08-25 15:09:53.122][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_dict_data`

[2025-08-25 15:09:53.124][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_dict_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.126][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_dict_data' ORDER BY event_object_table

[2025-08-25 15:09:53.138][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_dict\_type'

[2025-08-25 15:09:53.141][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_dict_type` 

[2025-08-25 15:09:53.142][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_type`

[2025-08-25 15:09:53.145][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_dict_type`

[2025-08-25 15:09:53.147][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_dict_type' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.149][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_dict_type' ORDER BY event_object_table

[2025-08-25 15:09:53.158][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_job'

[2025-08-25 15:09:53.16][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_job` 

[2025-08-25 15:09:53.161][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job`

[2025-08-25 15:09:53.165][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_job`

[2025-08-25 15:09:53.167][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_job' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.169][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_job' ORDER BY event_object_table

[2025-08-25 15:09:53.177][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_job\_log'

[2025-08-25 15:09:53.179][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_job_log` 

[2025-08-25 15:09:53.18][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job_log`

[2025-08-25 15:09:53.183][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_job_log`

[2025-08-25 15:09:53.185][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_job_log' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.187][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_job_log' ORDER BY event_object_table

[2025-08-25 15:09:53.194][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_logininfor'

[2025-08-25 15:09:53.196][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_logininfor` 

[2025-08-25 15:09:53.197][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_logininfor`

[2025-08-25 15:09:53.2][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_logininfor`

[2025-08-25 15:09:53.203][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_logininfor' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.204][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_logininfor' ORDER BY event_object_table

[2025-08-25 15:09:53.213][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_menu'

[2025-08-25 15:09:53.216][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_menu` 

[2025-08-25 15:09:53.217][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-25 15:09:53.222][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-25 15:09:53.224][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_menu' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.226][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_menu' ORDER BY event_object_table

[2025-08-25 15:09:53.246][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_notice'

[2025-08-25 15:09:53.249][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_notice` 

[2025-08-25 15:09:53.251][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_notice`

[2025-08-25 15:09:53.254][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_notice`

[2025-08-25 15:09:53.256][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_notice' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.258][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_notice' ORDER BY event_object_table

[2025-08-25 15:09:53.268][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_oper\_log'

[2025-08-25 15:09:53.27][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_oper_log` 

[2025-08-25 15:09:53.271][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_oper_log`

[2025-08-25 15:09:53.276][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_oper_log`

[2025-08-25 15:09:53.278][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_oper_log' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.28][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_oper_log' ORDER BY event_object_table

[2025-08-25 15:09:53.306][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_post'

[2025-08-25 15:09:53.308][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_post` 

[2025-08-25 15:09:53.309][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_post`

[2025-08-25 15:09:53.312][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_post`

[2025-08-25 15:09:53.315][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_post' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.316][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_post' ORDER BY event_object_table

[2025-08-25 15:09:53.325][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_role'

[2025-08-25 15:09:53.327][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_role` 

[2025-08-25 15:09:53.328][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role`

[2025-08-25 15:09:53.332][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_role`

[2025-08-25 15:09:53.335][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_role' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.336][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_role' ORDER BY event_object_table

[2025-08-25 15:09:53.346][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_role\_dept'

[2025-08-25 15:09:53.349][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_role_dept` 

[2025-08-25 15:09:53.35][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_dept`

[2025-08-25 15:09:53.352][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_role_dept`

[2025-08-25 15:09:53.354][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_role_dept' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.356][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_role_dept' ORDER BY event_object_table

[2025-08-25 15:09:53.362][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_role\_menu'

[2025-08-25 15:09:53.365][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_role_menu` 

[2025-08-25 15:09:53.366][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_menu`

[2025-08-25 15:09:53.368][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_role_menu`

[2025-08-25 15:09:53.37][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_role_menu' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.372][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_role_menu' ORDER BY event_object_table

[2025-08-25 15:09:53.378][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_user'

[2025-08-25 15:09:53.381][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_user` 

[2025-08-25 15:09:53.382][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-25 15:09:53.387][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-25 15:09:53.389][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_user' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.391][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_user' ORDER BY event_object_table

[2025-08-25 15:09:53.403][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_user\_post'

[2025-08-25 15:09:53.405][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_user_post` 

[2025-08-25 15:09:53.406][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_post`

[2025-08-25 15:09:53.408][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_user_post`

[2025-08-25 15:09:53.41][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_user_post' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.412][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_user_post' ORDER BY event_object_table

[2025-08-25 15:09:53.418][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_user\_role'

[2025-08-25 15:09:53.42][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `sys_user_role` 

[2025-08-25 15:09:53.421][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_role`

[2025-08-25 15:09:53.423][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `sys_user_role`

[2025-08-25 15:09:53.425][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_user_role' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.427][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_user_role' ORDER BY event_object_table

[2025-08-25 15:09:53.435][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'task'

[2025-08-25 15:09:53.437][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `task` 

[2025-08-25 15:09:53.438][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `task`

[2025-08-25 15:09:53.442][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `task`

[2025-08-25 15:09:53.444][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'task' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.446][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'task' ORDER BY event_object_table

[2025-08-25 15:09:53.456][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_asset'

[2025-08-25 15:09:53.458][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_asset` 

[2025-08-25 15:09:53.459][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_asset`

[2025-08-25 15:09:53.462][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_asset`

[2025-08-25 15:09:53.465][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_asset' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.466][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_asset' ORDER BY event_object_table

[2025-08-25 15:09:53.475][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_follow'

[2025-08-25 15:09:53.477][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_follow` 

[2025-08-25 15:09:53.478][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_follow`

[2025-08-25 15:09:53.48][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_follow`

[2025-08-25 15:09:53.483][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_follow' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.485][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_follow' ORDER BY event_object_table

[2025-08-25 15:09:53.492][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_level'

[2025-08-25 15:09:53.494][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_level` 

[2025-08-25 15:09:53.495][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_level`

[2025-08-25 15:09:53.498][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_level`

[2025-08-25 15:09:53.5][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_level' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.502][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_level' ORDER BY event_object_table

[2025-08-25 15:09:53.51][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_medal'

[2025-08-25 15:09:53.512][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_medal` 

[2025-08-25 15:09:53.513][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_medal`

[2025-08-25 15:09:53.516][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_medal`

[2025-08-25 15:09:53.519][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_medal' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.521][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_medal' ORDER BY event_object_table

[2025-08-25 15:09:53.527][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_recharge\_record'

[2025-08-25 15:09:53.529][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_recharge_record` 

[2025-08-25 15:09:53.53][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_recharge_record`

[2025-08-25 15:09:53.533][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_recharge_record`

[2025-08-25 15:09:53.535][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_recharge_record' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.537][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_recharge_record' ORDER BY event_object_table

[2025-08-25 15:09:53.545][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_task\_completion'

[2025-08-25 15:09:53.548][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_task_completion` 

[2025-08-25 15:09:53.549][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_task_completion`

[2025-08-25 15:09:53.552][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_task_completion`

[2025-08-25 15:09:53.555][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_task_completion' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.557][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_task_completion' ORDER BY event_object_table

[2025-08-25 15:09:53.565][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_vip'

[2025-08-25 15:09:53.567][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `user_vip` 

[2025-08-25 15:09:53.568][本地Mysql8][000573][MYSQL][]
SHOW FULL COLUMNS FROM `user_vip`

[2025-08-25 15:09:53.571][本地Mysql8][000573][MYSQL][]
SHOW INDEX FROM `user_vip`

[2025-08-25 15:09:53.574][本地Mysql8][000573][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_vip' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-25 15:09:53.575][本地Mysql8][000573][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_vip' ORDER BY event_object_table

[2025-08-25 16:45:52.874][本地Mysql8][000572][MYSQL][]
SELECT * FROM `izhule`.`merchant_tag` LIMIT 0,1000

[2025-08-25 16:45:52.876][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant_tag'

[2025-08-25 16:45:52.882][本地Mysql8][000640][MYSQL][]
SHOW COLUMNS FROM `izhule`.`merchant_tag`

[2025-08-25 16:45:52.885][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `izhule`.`merchant_tag`

[2025-08-25 16:45:52.886][本地Mysql8][000640][MYSQL][]
SHOW INDEX FROM `merchant_tag`

[2025-08-25 16:45:52.889][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_tag`

[2025-08-25 16:56:22.261][本地Mysql8][000572][MYSQL][]
SELECT * FROM `izhule`.`goods_tag` LIMIT 0,1000

[2025-08-25 16:56:22.263][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS LIKE 'goods_tag'

[2025-08-25 16:56:22.267][本地Mysql8][000640][MYSQL][]
SHOW COLUMNS FROM `izhule`.`goods_tag`

[2025-08-25 16:56:22.269][本地Mysql8][000573][MYSQL][]
SHOW CREATE TABLE `izhule`.`goods_tag`

[2025-08-25 16:56:22.27][本地Mysql8][000640][MYSQL][]
SHOW INDEX FROM `goods_tag`

[2025-08-25 16:56:22.273][本地Mysql8][000572][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tag`

[2025-08-25 16:58:27.493][腾讯_上门按摩][000405][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 16:58:27.601][腾讯_上门按摩][000405][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 16:58:34.883][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 16:58:34.901][本地Mysql8][000572][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 17:04:54.516][本地Mysql8][000572][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-25 17:04:54.592][本地Mysql8][000572][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 17:04:54.622][本地Mysql8][000572][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-25 17:05:06.144][本地Mysql8][000572][MYSQL][]
SET PROFILING = 1

[2025-08-25 17:05:06.144][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:05:06.153][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:05:06.164][本地Mysql8][000572][MYSQL][]
-- 选项卡表
DROP TABLE IF EXISTS `tabs`

[2025-08-25 17:05:06.168][本地Mysql8][000572][MYSQL][]
CREATE TABLE `tabs`  (
  `id` varchar(36) NOT NULL COMMENT '选项卡ID',
  `name` varchar(50) NOT NULL COMMENT '选项卡名称',
  `tab_type` varchar(20) NOT NULL COMMENT '选项卡类型(goods商品 merchant商家 note笔记等)',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` varchar(10) DEFAULT '1' COMMENT '状态(0禁用 1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tab_type` (`tab_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='选项卡表'

[2025-08-25 17:05:06.203][本地Mysql8][000572][MYSQL][]
-- 插入初始数据
INSERT INTO `tab` (`id`, `name`, `tab_type`, `sort`, `status`) VALUES
-- 商品类型选项卡
('goods_001', '全部商品', 'goods', 1, '1'),
('goods_002', '热门商品', 'goods', 2, '1'),
('goods_003', '新品推荐', 'goods', 3, '1'),
('goods_004', '限时特价', 'goods', 4, '1'),
('goods_005', '团购优惠', 'goods', 5, '1'),

-- 商家类型选项卡
('merchant_001', '全部商家', 'merchant', 1, '1'),
('merchant_002', '神券商家', 'merchant', 2, '1'),
('merchant_003', '综合清吧', 'merchant', 3, '1'),
('merchant_004', '精酿啤酒吧', 'merchant', 4, '1'),
('merchant_005', '威士忌吧', 'merchant', 5, '1'),
('merchant_006', '推荐商家', 'merchant', 6, '1'),
('merchant_007', '热门商家', 'merchant', 7, '1'),
('merchant_008', '新店推荐', 'merchant', 8, '1'),

-- 笔记类型选项卡
('note_001', '最新推荐', 'note', 1, '1'),
('note_002', '热门推荐', 'note', 2, '1'),
('note_003', '团购推荐', 'note', 3, '1'),
('note_004', '酒吧探店', 'note', 4, '1'),
('note_005', '美食分享', 'note', 5, '1')

[2025-08-25 17:05:06.204][本地Mysql8][000572][MYSQL][]
Table 'izhule.tab' doesn't exist

[2025-08-25 17:05:14.662][本地Mysql8][000572][MYSQL][]
SET PROFILING = 1

[2025-08-25 17:05:14.663][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:05:14.672][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:05:14.682][本地Mysql8][000572][MYSQL][]
-- 选项卡表
DROP TABLE IF EXISTS `tabs`

[2025-08-25 17:05:14.697][本地Mysql8][000572][MYSQL][]
CREATE TABLE `tabs`  (
  `id` varchar(36) NOT NULL COMMENT '选项卡ID',
  `name` varchar(50) NOT NULL COMMENT '选项卡名称',
  `tab_type` varchar(20) NOT NULL COMMENT '选项卡类型(goods商品 merchant商家 note笔记等)',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` varchar(10) DEFAULT '1' COMMENT '状态(0禁用 1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tab_type` (`tab_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='选项卡表'

[2025-08-25 17:05:14.739][本地Mysql8][000572][MYSQL][]
-- 插入初始数据
INSERT INTO `tabs` (`id`, `name`, `tab_type`, `sort`, `status`) VALUES
-- 商品类型选项卡
('goods_001', '全部商品', 'goods', 1, '1'),
('goods_002', '热门商品', 'goods', 2, '1'),
('goods_003', '新品推荐', 'goods', 3, '1'),
('goods_004', '限时特价', 'goods', 4, '1'),
('goods_005', '团购优惠', 'goods', 5, '1'),

-- 商家类型选项卡
('merchant_001', '全部商家', 'merchant', 1, '1'),
('merchant_002', '神券商家', 'merchant', 2, '1'),
('merchant_003', '综合清吧', 'merchant', 3, '1'),
('merchant_004', '精酿啤酒吧', 'merchant', 4, '1'),
('merchant_005', '威士忌吧', 'merchant', 5, '1'),
('merchant_006', '推荐商家', 'merchant', 6, '1'),
('merchant_007', '热门商家', 'merchant', 7, '1'),
('merchant_008', '新店推荐', 'merchant', 8, '1'),

-- 笔记类型选项卡
('note_001', '最新推荐', 'note', 1, '1'),
('note_002', '热门推荐', 'note', 2, '1'),
('note_003', '团购推荐', 'note', 3, '1'),
('note_004', '酒吧探店', 'note', 4, '1'),
('note_005', '美食分享', 'note', 5, '1')

[2025-08-25 17:05:14.746][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:05:14.755][本地Mysql8][000572][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-25 17:05:14.757][本地Mysql8][000572][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.001268*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=2 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-25 17:07:59.431][本地Mysql8][000572][MYSQL][]
SET PROFILING = 1

[2025-08-25 17:07:59.432][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:07:59.441][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:07:59.451][本地Mysql8][000572][MYSQL][]
-- 商品选项卡关联表
DROP TABLE IF EXISTS `goods_tab_relation`

[2025-08-25 17:07:59.455][本地Mysql8][000572][MYSQL][]
CREATE TABLE `goods_tab_relation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `goods_id` varchar(36) NOT NULL COMMENT '商品ID',
    `tab_id` varchar(36) NOT NULL COMMENT '选项卡ID',
    `sort_order` int DEFAULT 0 COMMENT '在该选项卡中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_goods_tab` (`goods_id`, `tab_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_tab_id` (`tab_id`)
) ENGINE=InnoDB COMMENT='商品选项卡关联表'

[2025-08-25 17:07:59.493][本地Mysql8][000572][MYSQL][]
-- 商家选项卡关联表
DROP TABLE IF EXISTS `merchant_tab_relation`

[2025-08-25 17:07:59.497][本地Mysql8][000572][MYSQL][]
CREATE TABLE `merchant_tab_relation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `merchant_id` varchar(36) NOT NULL COMMENT '商家ID',
    `tab_id` varchar(36) NOT NULL COMMENT '选项卡ID',
    `sort_order` int DEFAULT 0 COMMENT '在该选项卡中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_merchant_tab` (`merchant_id`, `tab_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_tab_id` (`tab_id`)
) ENGINE=InnoDB COMMENT='商家选项卡关联表'

[2025-08-25 17:07:59.537][本地Mysql8][000572][MYSQL][]
-- 笔记选项卡关联表
DROP TABLE IF EXISTS `note_tab_relation`

[2025-08-25 17:07:59.541][本地Mysql8][000572][MYSQL][]
CREATE TABLE `note_tab_relation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `note_id` varchar(36) NOT NULL COMMENT '笔记ID',
    `tab_id` varchar(36) NOT NULL COMMENT '选项卡ID',
    `sort_order` int DEFAULT 0 COMMENT '在该选项卡中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_note_tab` (`note_id`, `tab_id`),
    KEY `idx_note_id` (`note_id`),
    KEY `idx_tab_id` (`tab_id`)
) ENGINE=InnoDB COMMENT='笔记选项卡关联表'

[2025-08-25 17:07:59.576][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:07:59.586][本地Mysql8][000572][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-25 17:07:59.587][本地Mysql8][000572][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.006465*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=11 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-25 17:08:12.272][本地Mysql8][000573][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`goods_tag`;

[2025-08-25 17:08:16.717][本地Mysql8][000573][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`goods_tag_relation`;

[2025-08-25 17:08:25.781][本地Mysql8][000573][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`merchant_tag`;

[2025-08-25 17:12:20.705][本地Mysql8][000573][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 17:12:20.716][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 17:12:24.205][本地Mysql8][000573][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`goods_tab_relation`;

[2025-08-25 17:12:30.073][本地Mysql8][000573][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`merchant_tab_relation`;

[2025-08-25 17:12:33.927][本地Mysql8][000573][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`note_tab_relation`;

[2025-08-25 17:14:53.106][本地Mysql8][000572][MYSQL][]
SET PROFILING = 1

[2025-08-25 17:14:53.107][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:14:53.116][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:14:53.128][本地Mysql8][000572][MYSQL][]
-- 商品选项卡关联表
DROP TABLE IF EXISTS `goods_tabs`

[2025-08-25 17:14:53.133][本地Mysql8][000572][MYSQL][]
CREATE TABLE `goods_tabs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `goods_id` varchar(36) NOT NULL COMMENT '商品ID',
    `tabs_id` varchar(36) NOT NULL COMMENT '选项卡ID',
    `sort_order` int DEFAULT 0 COMMENT '在该选项卡中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_goods_tabs` (`goods_id`, `tabs_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_tabs_id` (`tabs_id`)
) ENGINE=InnoDB COMMENT='商品选项卡关联表'

[2025-08-25 17:14:53.169][本地Mysql8][000572][MYSQL][]
-- 商家选项卡关联表
DROP TABLE IF EXISTS `merchant_tabs`

[2025-08-25 17:14:53.172][本地Mysql8][000572][MYSQL][]
CREATE TABLE `merchant_tabs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `merchant_id` varchar(36) NOT NULL COMMENT '商家ID',
    `tabs_id` varchar(36) NOT NULL COMMENT '选项卡ID',
    `sort_order` int DEFAULT 0 COMMENT '在该选项卡中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_merchant_tabs` (`merchant_id`, `tabs_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_tabs_id` (`tabs_id`)
) ENGINE=InnoDB COMMENT='商家选项卡关联表'

[2025-08-25 17:14:53.216][本地Mysql8][000572][MYSQL][]
-- 笔记选项卡关联表
DROP TABLE IF EXISTS `note_tabs`

[2025-08-25 17:14:53.22][本地Mysql8][000572][MYSQL][]
CREATE TABLE `note_tabs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `note_id` varchar(36) NOT NULL COMMENT '笔记ID',
    `tabs_id` varchar(36) NOT NULL COMMENT '选项卡ID',
    `sort_order` int DEFAULT 0 COMMENT '在该选项卡中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_note_tabs` (`note_id`, `tabs_id`),
    KEY `idx_note_id` (`note_id`),
    KEY `idx_tabs_id` (`tabs_id`)
) ENGINE=InnoDB COMMENT='笔记选项卡关联表'

[2025-08-25 17:14:53.262][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:14:53.272][本地Mysql8][000572][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-25 17:14:53.273][本地Mysql8][000572][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.035033*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=23 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-25 17:14:59.977][本地Mysql8][000573][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 17:14:59.988][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 17:15:31.745][本地Mysql8][000573][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-25 17:15:31.754][本地Mysql8][000573][MYSQL][]
SHOW TABLE STATUS

[2025-08-25 17:19:23.41][本地Mysql8][000572][MYSQL][]
SET PROFILING = 1

[2025-08-25 17:19:23.411][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:19:23.42][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:19:23.431][本地Mysql8][000572][MYSQL][]
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡', '3', '1', 'tabs', 'zhule/tabs/index', 1, 0, 'C', '0', '0', 'zhule:tabs:list', '#', 'admin', sysdate(), '', null, '选项卡菜单')

[2025-08-25 17:19:23.435][本地Mysql8][000572][MYSQL][]
-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID()

[2025-08-25 17:19:23.439][本地Mysql8][000572][MYSQL][]
-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tabs:query',        '#', 'admin', sysdate(), '', null, '')

[2025-08-25 17:19:23.443][本地Mysql8][000572][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tabs:add',          '#', 'admin', sysdate(), '', null, '')

[2025-08-25 17:19:23.446][本地Mysql8][000572][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tabs:edit',         '#', 'admin', sysdate(), '', null, '')

[2025-08-25 17:19:23.449][本地Mysql8][000572][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tabs:remove',       '#', 'admin', sysdate(), '', null, '')

[2025-08-25 17:19:23.452][本地Mysql8][000572][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tabs:export',       '#', 'admin', sysdate(), '', null, '')

[2025-08-25 17:19:23.455][本地Mysql8][000572][MYSQL][]
SHOW STATUS

[2025-08-25 17:19:23.464][本地Mysql8][000572][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-25 17:19:23.465][本地Mysql8][000572][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.001528*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=36 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-26 08:46:39.396][本地Mysql8][000644][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 08:46:39.414][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 08:46:51.498][本地Mysql8][000644][MYSQL][]
SHOW PROCEDURE STATUS WHERE Db = 'izhule'

[2025-08-26 08:46:51.5][本地Mysql8][000644][MYSQL][]
SHOW FUNCTION STATUS WHERE Db = 'izhule'

[2025-08-26 08:46:51.501][本地Mysql8][000644][MYSQL][]
SELECT * FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'izhule' ORDER BY ROUTINE_NAME

[2025-08-26 08:46:53.383][本地Mysql8][000644][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 08:46:53.393][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 08:47:02.506][本地Mysql8][000644][MYSQL][]
SELECT * FROM `izhule`.`sys_user` LIMIT 0,1000

[2025-08-26 08:47:02.508][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_user'

[2025-08-26 08:47:02.519][本地Mysql8][000646][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_user`

[2025-08-26 08:47:02.523][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_user`

[2025-08-26 08:47:02.524][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-26 08:47:02.529][本地Mysql8][000644][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-26 08:49:49.573][本地Mysql8][000644][MYSQL][]
UPDATE `izhule`.`sys_user` SET `user_name` = '13800138000', `nick_name` = '助乐测试号', `phonenumber` = '13800138000', `avatar` = '/profile/avatar/pic_0002.jpg' WHERE `user_id` = 100

[2025-08-26 08:49:49.577][本地Mysql8][000644][MYSQL][]
SELECT * FROM `izhule`.`sys_user` WHERE `user_id` = 100

[2025-08-26 08:58:20.753][本地Mysql8][000644][MYSQL][]
SELECT * FROM `izhule`.`sys_menu` LIMIT 0,1000

[2025-08-26 08:58:20.756][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_menu'

[2025-08-26 08:58:20.76][本地Mysql8][000646][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_menu`

[2025-08-26 08:58:20.764][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_menu`

[2025-08-26 08:58:20.765][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-26 08:58:20.767][本地Mysql8][000644][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-26 08:58:27.275][本地Mysql8][000644][MYSQL][]
SELECT * FROM `izhule`.`sys_menu` LIMIT 1000,1000

[2025-08-26 08:58:27.278][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_menu'

[2025-08-26 08:58:27.282][本地Mysql8][000646][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_menu`

[2025-08-26 08:58:27.285][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_menu`

[2025-08-26 08:58:27.286][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-26 08:58:27.288][本地Mysql8][000644][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-26 08:58:29.389][本地Mysql8][000644][MYSQL][]
SELECT * FROM `izhule`.`sys_menu` LIMIT 0,1000

[2025-08-26 08:58:29.392][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_menu'

[2025-08-26 08:58:29.396][本地Mysql8][000646][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_menu`

[2025-08-26 08:58:29.399][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_menu`

[2025-08-26 08:58:29.4][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-26 08:58:29.402][本地Mysql8][000644][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-26 09:02:59.597][本地Mysql8][000644][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 09:02:59.608][本地Mysql8][000644][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 09:03:04.138][本地Mysql8][000644][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`merchant_tabs`;

[2025-08-26 09:03:07.018][本地Mysql8][000644][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`goods_tabs`;

[2025-08-26 09:03:12.266][本地Mysql8][000644][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`tabs`;

[2025-08-26 09:03:18.522][本地Mysql8][000644][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`note_tabs`;

[2025-08-26 09:03:25.655][本地Mysql8][000644][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-26 09:03:25.724][本地Mysql8][000644][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 09:03:25.734][本地Mysql8][000644][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-26 09:03:28.609][本地Mysql8][000644][MYSQL][]
SET PROFILING = 1

[2025-08-26 09:03:28.61][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:03:28.618][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:03:28.629][本地Mysql8][000644][MYSQL][]
-- 选项卡表
DROP TABLE IF EXISTS `tags`

[2025-08-26 09:03:28.634][本地Mysql8][000644][MYSQL][]
CREATE TABLE `tags`  (
  `id` varchar(36) NOT NULL COMMENT '选项卡ID',
  `name` varchar(50) NOT NULL COMMENT '选项卡名称',
  `tag_type` varchar(20) NOT NULL COMMENT '选项卡类型(goods商品 merchant商家 note笔记等 fun好玩功能)',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` varchar(10) DEFAULT '1' COMMENT '状态(0禁用 1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tag_type` (`tag_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='选项卡表'

[2025-08-26 09:03:28.667][本地Mysql8][000644][MYSQL][]
-- 插入初始数据
INSERT INTO `tags` (`id`, `name`, `tag_type`, `sort`, `status`) VALUES
-- 商品类型选项卡
('goods_001', '全部商品', 'goods', 1, '1'),
('goods_002', '热门商品', 'goods', 2, '1'),
('goods_003', '新品推荐', 'goods', 3, '1'),
('goods_004', '限时特价', 'goods', 4, '1'),
('goods_005', '团购优惠', 'goods', 5, '1'),

-- 商家类型选项卡
('merchant_001', '全部商家', 'merchant', 1, '1'),
('merchant_002', '神券商家', 'merchant', 2, '1'),
('merchant_003', '综合清吧', 'merchant', 3, '1'),
('merchant_004', '精酿啤酒吧', 'merchant', 4, '1'),
('merchant_005', '威士忌吧', 'merchant', 5, '1'),
('merchant_006', '推荐商家', 'merchant', 6, '1'),
('merchant_007', '热门商家', 'merchant', 7, '1'),
('merchant_008', '新店推荐', 'merchant', 8, '1'),

-- 笔记类型选项卡
('note_001', '最新推荐', 'note', 1, '1'),
('note_002', '热门推荐', 'note', 2, '1'),
('note_003', '团购推荐', 'note', 3, '1'),
('note_004', '酒吧探店', 'note', 4, '1'),
('note_005', '美食分享', 'note', 5, '1')

[2025-08-26 09:03:28.672][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:03:28.681][本地Mysql8][000644][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-26 09:03:28.682][本地Mysql8][000644][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.000947*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=2 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-26 09:03:51.228][本地Mysql8][000644][MYSQL][]
SET PROFILING = 1

[2025-08-26 09:03:51.229][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:03:51.238][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:03:51.249][本地Mysql8][000644][MYSQL][]
-- 商品标签关联表
DROP TABLE IF EXISTS `goods_tags`

[2025-08-26 09:03:51.253][本地Mysql8][000644][MYSQL][]
CREATE TABLE `goods_tags` (
    `id` varchar(36) NOT NULL COMMENT '关联ID',
    `goods_id` varchar(36) NOT NULL COMMENT '商品ID',
    `tags_id` varchar(36) NOT NULL COMMENT '标签ID',
    `sort_order` int DEFAULT 0 COMMENT '在该标签中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_goods_tags` (`goods_id`, `tags_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_tags_id` (`tags_id`)
) ENGINE=InnoDB COMMENT='商品标签关联表'

[2025-08-26 09:03:51.292][本地Mysql8][000644][MYSQL][]
-- 商家标签关联表
DROP TABLE IF EXISTS `merchant_tags`

[2025-08-26 09:03:51.296][本地Mysql8][000644][MYSQL][]
CREATE TABLE `merchant_tags` (
    `id` varchar(36) NOT NULL COMMENT '关联ID',
    `merchant_id` varchar(36) NOT NULL COMMENT '商家ID',
    `tags_id` varchar(36) NOT NULL COMMENT '标签ID',
    `sort_order` int DEFAULT 0 COMMENT '在该标签中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_merchant_tags` (`merchant_id`, `tags_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_tags_id` (`tags_id`)
) ENGINE=InnoDB COMMENT='商家标签关联表'

[2025-08-26 09:03:51.336][本地Mysql8][000644][MYSQL][]
-- 笔记标签关联表
DROP TABLE IF EXISTS `note_tags`

[2025-08-26 09:03:51.34][本地Mysql8][000644][MYSQL][]
CREATE TABLE `note_tags` (
    `id` varchar(36) NOT NULL COMMENT '关联ID',
    `note_id` varchar(36) NOT NULL COMMENT '笔记ID',
    `tags_id` varchar(36) NOT NULL COMMENT '标签ID',
    `sort_order` int DEFAULT 0 COMMENT '在该标签中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_note_tags` (`note_id`, `tags_id`),
    KEY `idx_note_id` (`note_id`),
    KEY `idx_tags_id` (`tags_id`)
) ENGINE=InnoDB COMMENT='笔记标签关联表'

[2025-08-26 09:03:51.384][本地Mysql8][000644][MYSQL][]
-- 好玩功能标签关联表
DROP TABLE IF EXISTS `fun_tags`

[2025-08-26 09:03:51.388][本地Mysql8][000644][MYSQL][]
CREATE TABLE `fun_tags` (
    `id` varchar(36) NOT NULL COMMENT '关联ID',
    `fun_id` varchar(36) NOT NULL COMMENT '好玩功能ID',
    `tags_id` varchar(36) NOT NULL COMMENT '标签ID',
    `sort_order` int DEFAULT 0 COMMENT '在该标签中的排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_fun_tags` (`fun_id`, `tags_id`),
    KEY `idx_fun_id` (`fun_id`),
    KEY `idx_tags_id` (`tags_id`)
) ENGINE=InnoDB COMMENT='好玩功能标签关联表'

[2025-08-26 09:03:51.424][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:03:51.433][本地Mysql8][000644][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-26 09:03:51.435][本地Mysql8][000644][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.000618*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=7 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-26 09:10:23.812][本地Mysql8][000644][MYSQL][]
SET PROFILING = 1

[2025-08-26 09:10:23.812][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:10:23.822][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:10:23.834][本地Mysql8][000644][MYSQL][]
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡', '3', '1', 'tags', 'zhule/tags/index', 1, 0, 'C', '0', '0', 'zhule:tags:list', '#', 'admin', sysdate(), '', null, '选项卡菜单')

[2025-08-26 09:10:23.837][本地Mysql8][000644][MYSQL][]
-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID()

[2025-08-26 09:10:23.838][本地Mysql8][000644][MYSQL][]
-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:query',        '#', 'admin', sysdate(), '', null, '')

[2025-08-26 09:10:23.841][本地Mysql8][000644][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:add',          '#', 'admin', sysdate(), '', null, '')

[2025-08-26 09:10:23.843][本地Mysql8][000644][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:edit',         '#', 'admin', sysdate(), '', null, '')

[2025-08-26 09:10:23.847][本地Mysql8][000644][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:remove',       '#', 'admin', sysdate(), '', null, '')

[2025-08-26 09:10:23.85][本地Mysql8][000644][MYSQL][]
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('选项卡导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'zhule:tags:export',       '#', 'admin', sysdate(), '', null, '')

[2025-08-26 09:10:23.852][本地Mysql8][000644][MYSQL][]
SHOW STATUS

[2025-08-26 09:10:23.861][本地Mysql8][000644][MYSQL][]
SELECT QUERY_ID, SUM(DURATION) AS SUM_DURATION FROM INFORMATION_SCHEMA.PROFILING GROUP BY QUERY_ID

[2025-08-26 09:10:23.863][本地Mysql8][000644][MYSQL][]
SELECT STATE AS `Status`, ROUND(SUM(DURATION),7) AS `Duration`, CONCAT(ROUND(SUM(DURATION)/0.001255*100,3), '') AS `Percentage` FROM INFORMATION_SCHEMA.PROFILING WHERE QUERY_ID=20 GROUP BY SEQ, STATE ORDER BY SEQ

[2025-08-26 09:11:35.803][本地Mysql8][000645][MYSQL][]
SELECT * FROM `izhule`.`sys_menu` LIMIT 0,1000

[2025-08-26 09:11:35.807][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS LIKE 'sys_menu'

[2025-08-26 09:11:35.814][本地Mysql8][000652][MYSQL][]
SHOW COLUMNS FROM `izhule`.`sys_menu`

[2025-08-26 09:11:35.817][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `izhule`.`sys_menu`

[2025-08-26 09:11:35.819][本地Mysql8][000652][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-26 09:11:35.821][本地Mysql8][000645][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-26 09:11:45.764][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2315

[2025-08-26 09:11:45.768][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2316

[2025-08-26 09:11:45.77][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2317

[2025-08-26 09:11:45.773][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2318

[2025-08-26 09:11:45.775][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2319

[2025-08-26 09:11:45.778][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2320

[2025-08-26 09:11:45.781][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2321

[2025-08-26 09:11:45.784][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2322

[2025-08-26 09:11:45.786][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2323

[2025-08-26 09:11:45.788][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2324

[2025-08-26 09:11:45.791][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2325

[2025-08-26 09:11:45.794][本地Mysql8][000645][MYSQL][]
DELETE FROM `izhule`.`sys_menu` WHERE `menu_id` = 2326

[2025-08-26 09:12:04.398][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 09:12:04.408][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 09:12:10.549][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS LIKE 'tags'

[2025-08-26 09:12:10.552][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `tags` 

[2025-08-26 09:12:10.553][本地Mysql8][000645][MYSQL][]
SHOW FULL COLUMNS FROM `tags`

[2025-08-26 09:12:10.557][本地Mysql8][000645][MYSQL][]
SHOW INDEX FROM `tags`

[2025-08-26 09:12:10.567][本地Mysql8][000645][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'tags' ORDER BY event_object_table

[2025-08-26 09:12:10.569][本地Mysql8][000645][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 09:12:10.796][本地Mysql8][000645][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-26 09:12:31.047][本地Mysql8][000645][MYSQL][]
ALTER TABLE `izhule`.`tags` 
MODIFY COLUMN `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签ID' FIRST,
MODIFY COLUMN `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称' AFTER `id`,
MODIFY COLUMN `tag_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签类型(goods商品 merchant商家 note笔记等 fun好玩功能)' AFTER `name`,
COMMENT = '标签表'

[2025-08-26 09:12:31.058][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 09:12:31.07][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS LIKE 'tags'

[2025-08-26 09:12:31.074][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `tags` 

[2025-08-26 09:12:31.075][本地Mysql8][000645][MYSQL][]
SHOW FULL COLUMNS FROM `tags`

[2025-08-26 09:12:31.079][本地Mysql8][000645][MYSQL][]
SHOW INDEX FROM `tags`

[2025-08-26 09:12:31.082][本地Mysql8][000645][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'tags' ORDER BY event_object_table

[2025-08-26 09:12:31.084][本地Mysql8][000645][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 09:29:50.889][本地Mysql8][000645][MYSQL][]
SELECT * FROM `izhule`.`banner` LIMIT 0,1000

[2025-08-26 09:29:50.891][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS LIKE 'banner'

[2025-08-26 09:29:50.896][本地Mysql8][000652][MYSQL][]
SHOW COLUMNS FROM `izhule`.`banner`

[2025-08-26 09:29:50.898][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `izhule`.`banner`

[2025-08-26 09:29:50.899][本地Mysql8][000652][MYSQL][]
SHOW INDEX FROM `banner`

[2025-08-26 09:29:50.91][本地Mysql8][000645][MYSQL][]
SHOW FULL COLUMNS FROM `banner`

[2025-08-26 09:30:08.251][本地Mysql8][000645][MYSQL][]
UPDATE `izhule`.`banner` SET `banner_type` = 'mall' WHERE `id` = '1955949779961319426'

[2025-08-26 09:30:08.255][本地Mysql8][000645][MYSQL][]
SELECT * FROM `izhule`.`banner` WHERE `id` = '1955949779961319426'

[2025-08-26 10:01:47.712][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:01:47.751][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'izhule' ORDER BY TABLE_NAME ASC

[2025-08-26 10:01:47.756][本地Mysql8][000646][MYSQL][]
SHOW PROCEDURE STATUS WHERE Db = 'izhule'

[2025-08-26 10:01:47.758][本地Mysql8][000646][MYSQL][]
SHOW FUNCTION STATUS WHERE Db = 'izhule'

[2025-08-26 10:01:47.76][本地Mysql8][000646][MYSQL][]
SELECT * FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'izhule' ORDER BY ROUTINE_NAME

[2025-08-26 10:01:47.763][本地Mysql8][000646][MYSQL][]
SELECT EVENT_CATALOG, EVENT_SCHEMA, EVENT_NAME, DEFINER, TIME_ZONE, EVENT_DEFINITION, EVENT_BODY, EVENT_TYPE, SQL_MODE, STATUS, EXECUTE_AT, INTERVAL_VALUE, INTERVAL_FIELD, STARTS, ENDS, ON_COMPLETION, CREATED, LAST_ALTERED, LAST_EXECUTED, ORIGINATOR, CHARACTER_SET_CLIENT, COLLATION_CONNECTION, DATABASE_COLLATION, EVENT_COMMENT FROM information_schema.EVENTS WHERE EVENT_SCHEMA = 'izhule' ORDER BY EVENT_NAME ASC

[2025-08-26 10:01:47.77][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_BLOB\_TRIGGERS'

[2025-08-26 10:01:47.776][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_BLOB_TRIGGERS` 

[2025-08-26 10:01:47.778][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-26 10:01:47.781][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-26 10:01:47.783][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_BLOB_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.786][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_BLOB_TRIGGERS' ORDER BY event_object_table

[2025-08-26 10:01:47.796][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_CALENDARS'

[2025-08-26 10:01:47.799][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_CALENDARS` 

[2025-08-26 10:01:47.8][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CALENDARS`

[2025-08-26 10:01:47.803][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_CALENDARS`

[2025-08-26 10:01:47.807][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_CALENDARS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.809][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_CALENDARS' ORDER BY event_object_table

[2025-08-26 10:01:47.815][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_CRON\_TRIGGERS'

[2025-08-26 10:01:47.818][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_CRON_TRIGGERS` 

[2025-08-26 10:01:47.819][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_CRON_TRIGGERS`

[2025-08-26 10:01:47.823][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_CRON_TRIGGERS`

[2025-08-26 10:01:47.827][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_CRON_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.829][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_CRON_TRIGGERS' ORDER BY event_object_table

[2025-08-26 10:01:47.837][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_FIRED\_TRIGGERS'

[2025-08-26 10:01:47.843][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_FIRED_TRIGGERS` 

[2025-08-26 10:01:47.845][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-26 10:01:47.849][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-26 10:01:47.851][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_FIRED_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.854][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_FIRED_TRIGGERS' ORDER BY event_object_table

[2025-08-26 10:01:47.865][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_JOB\_DETAILS'

[2025-08-26 10:01:47.867][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_JOB_DETAILS` 

[2025-08-26 10:01:47.869][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_JOB_DETAILS`

[2025-08-26 10:01:47.874][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_JOB_DETAILS`

[2025-08-26 10:01:47.877][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_JOB_DETAILS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.879][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_JOB_DETAILS' ORDER BY event_object_table

[2025-08-26 10:01:47.892][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_LOCKS'

[2025-08-26 10:01:47.895][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_LOCKS` 

[2025-08-26 10:01:47.896][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_LOCKS`

[2025-08-26 10:01:47.898][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_LOCKS`

[2025-08-26 10:01:47.901][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_LOCKS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.904][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_LOCKS' ORDER BY event_object_table

[2025-08-26 10:01:47.912][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_PAUSED\_TRIGGER\_GRPS'

[2025-08-26 10:01:47.915][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` 

[2025-08-26 10:01:47.916][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-26 10:01:47.918][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-26 10:01:47.923][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_PAUSED_TRIGGER_GRPS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.925][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_PAUSED_TRIGGER_GRPS' ORDER BY event_object_table

[2025-08-26 10:01:47.933][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_SCHEDULER\_STATE'

[2025-08-26 10:01:47.935][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_SCHEDULER_STATE` 

[2025-08-26 10:01:47.937][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SCHEDULER_STATE`

[2025-08-26 10:01:47.94][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_SCHEDULER_STATE`

[2025-08-26 10:01:47.942][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_SCHEDULER_STATE' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.944][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_SCHEDULER_STATE' ORDER BY event_object_table

[2025-08-26 10:01:47.95][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_SIMPLE\_TRIGGERS'

[2025-08-26 10:01:47.954][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` 

[2025-08-26 10:01:47.956][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-26 10:01:47.959][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-26 10:01:47.961][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_SIMPLE_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.963][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_SIMPLE_TRIGGERS' ORDER BY event_object_table

[2025-08-26 10:01:47.97][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_SIMPROP\_TRIGGERS'

[2025-08-26 10:01:47.974][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` 

[2025-08-26 10:01:47.975][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-26 10:01:47.979][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-26 10:01:47.981][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_SIMPROP_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:47.983][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_SIMPROP_TRIGGERS' ORDER BY event_object_table

[2025-08-26 10:01:47.994][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'QRTZ\_TRIGGERS'

[2025-08-26 10:01:47.997][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `QRTZ_TRIGGERS` 

[2025-08-26 10:01:47.998][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `QRTZ_TRIGGERS`

[2025-08-26 10:01:48.002][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `QRTZ_TRIGGERS`

[2025-08-26 10:01:48.006][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'QRTZ_TRIGGERS' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.008][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'QRTZ_TRIGGERS' ORDER BY event_object_table

[2025-08-26 10:01:48.021][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'agent'

[2025-08-26 10:01:48.024][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `agent` 

[2025-08-26 10:01:48.025][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `agent`

[2025-08-26 10:01:48.031][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `agent`

[2025-08-26 10:01:48.034][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'agent' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.036][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'agent' ORDER BY event_object_table

[2025-08-26 10:01:48.054][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'agent\_application'

[2025-08-26 10:01:48.057][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `agent_application` 

[2025-08-26 10:01:48.058][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `agent_application`

[2025-08-26 10:01:48.063][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `agent_application`

[2025-08-26 10:01:48.066][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'agent_application' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.069][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'agent_application' ORDER BY event_object_table

[2025-08-26 10:01:48.088][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'agent\_commission\_record'

[2025-08-26 10:01:48.091][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `agent_commission_record` 

[2025-08-26 10:01:48.092][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `agent_commission_record`

[2025-08-26 10:01:48.096][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `agent_commission_record`

[2025-08-26 10:01:48.099][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'agent_commission_record' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.101][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'agent_commission_record' ORDER BY event_object_table

[2025-08-26 10:01:48.113][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'asset\_exchange'

[2025-08-26 10:01:48.116][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `asset_exchange` 

[2025-08-26 10:01:48.117][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `asset_exchange`

[2025-08-26 10:01:48.121][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `asset_exchange`

[2025-08-26 10:01:48.124][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'asset_exchange' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.125][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'asset_exchange' ORDER BY event_object_table

[2025-08-26 10:01:48.134][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'banner'

[2025-08-26 10:01:48.136][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `banner` 

[2025-08-26 10:01:48.138][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `banner`

[2025-08-26 10:01:48.141][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `banner`

[2025-08-26 10:01:48.144][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'banner' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.146][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'banner' ORDER BY event_object_table

[2025-08-26 10:01:48.154][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'city'

[2025-08-26 10:01:48.157][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `city` 

[2025-08-26 10:01:48.158][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `city`

[2025-08-26 10:01:48.161][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `city`

[2025-08-26 10:01:48.164][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'city' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.165][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'city' ORDER BY event_object_table

[2025-08-26 10:01:48.174][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'comment\_like'

[2025-08-26 10:01:48.176][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `comment_like` 

[2025-08-26 10:01:48.177][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `comment_like`

[2025-08-26 10:01:48.179][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `comment_like`

[2025-08-26 10:01:48.182][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.184][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'comment_like' ORDER BY event_object_table

[2025-08-26 10:01:48.191][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'flyway\_schema\_history'

[2025-08-26 10:01:48.193][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `flyway_schema_history` 

[2025-08-26 10:01:48.194][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `flyway_schema_history`

[2025-08-26 10:01:48.197][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `flyway_schema_history`

[2025-08-26 10:01:48.199][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'flyway_schema_history' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.201][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'flyway_schema_history' ORDER BY event_object_table

[2025-08-26 10:01:48.209][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun'

[2025-08-26 10:01:48.211][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun` 

[2025-08-26 10:01:48.213][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun`

[2025-08-26 10:01:48.217][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun`

[2025-08-26 10:01:48.22][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.222][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun' ORDER BY event_object_table

[2025-08-26 10:01:48.233][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_cart'

[2025-08-26 10:01:48.236][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_cart` 

[2025-08-26 10:01:48.238][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_cart`

[2025-08-26 10:01:48.241][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_cart`

[2025-08-26 10:01:48.244][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_cart' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.246][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_cart' ORDER BY event_object_table

[2025-08-26 10:01:48.256][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment'

[2025-08-26 10:01:48.259][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_comment` 

[2025-08-26 10:01:48.26][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment`

[2025-08-26 10:01:48.263][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_comment`

[2025-08-26 10:01:48.276][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.278][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment' ORDER BY event_object_table

[2025-08-26 10:01:48.287][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_comment\_like'

[2025-08-26 10:01:48.29][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_comment_like` 

[2025-08-26 10:01:48.291][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_comment_like`

[2025-08-26 10:01:48.294][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_comment_like`

[2025-08-26 10:01:48.297][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.299][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_comment_like' ORDER BY event_object_table

[2025-08-26 10:01:48.306][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_merchant'

[2025-08-26 10:01:48.309][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_merchant` 

[2025-08-26 10:01:48.31][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_merchant`

[2025-08-26 10:01:48.313][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_merchant`

[2025-08-26 10:01:48.316][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_merchant' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.318][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_merchant' ORDER BY event_object_table

[2025-08-26 10:01:48.331][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_order'

[2025-08-26 10:01:48.334][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_order` 

[2025-08-26 10:01:48.335][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order`

[2025-08-26 10:01:48.339][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_order`

[2025-08-26 10:01:48.341][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_order' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.343][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_order' ORDER BY event_object_table

[2025-08-26 10:01:48.353][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_order\_redemption'

[2025-08-26 10:01:48.355][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_order_redemption` 

[2025-08-26 10:01:48.357][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_order_redemption`

[2025-08-26 10:01:48.359][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_order_redemption`

[2025-08-26 10:01:48.362][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_order_redemption' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.364][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_order_redemption' ORDER BY event_object_table

[2025-08-26 10:01:48.373][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_rule'

[2025-08-26 10:01:48.375][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_rule` 

[2025-08-26 10:01:48.377][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_rule`

[2025-08-26 10:01:48.38][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_rule`

[2025-08-26 10:01:48.383][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_rule' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.384][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_rule' ORDER BY event_object_table

[2025-08-26 10:01:48.394][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'fun\_tags'

[2025-08-26 10:01:48.397][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `fun_tags` 

[2025-08-26 10:01:48.399][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `fun_tags`

[2025-08-26 10:01:48.401][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `fun_tags`

[2025-08-26 10:01:48.413][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'fun_tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.416][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'fun_tags' ORDER BY event_object_table

[2025-08-26 10:01:48.424][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'gen\_table'

[2025-08-26 10:01:48.427][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `gen_table` 

[2025-08-26 10:01:48.428][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table`

[2025-08-26 10:01:48.433][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `gen_table`

[2025-08-26 10:01:48.435][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'gen_table' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.437][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'gen_table' ORDER BY event_object_table

[2025-08-26 10:01:48.449][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'gen\_table\_column'

[2025-08-26 10:01:48.451][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `gen_table_column` 

[2025-08-26 10:01:48.453][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `gen_table_column`

[2025-08-26 10:01:48.458][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `gen_table_column`

[2025-08-26 10:01:48.46][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'gen_table_column' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.462][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'gen_table_column' ORDER BY event_object_table

[2025-08-26 10:01:48.475][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'goods'

[2025-08-26 10:01:48.478][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `goods` 

[2025-08-26 10:01:48.48][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `goods`

[2025-08-26 10:01:48.484][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `goods`

[2025-08-26 10:01:48.488][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.49][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods' ORDER BY event_object_table

[2025-08-26 10:01:48.501][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_category'

[2025-08-26 10:01:48.504][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `goods_category` 

[2025-08-26 10:01:48.506][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `goods_category`

[2025-08-26 10:01:48.508][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `goods_category`

[2025-08-26 10:01:48.511][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.513][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_category' ORDER BY event_object_table

[2025-08-26 10:01:48.52][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_comment'

[2025-08-26 10:01:48.523][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `goods_comment` 

[2025-08-26 10:01:48.524][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `goods_comment`

[2025-08-26 10:01:48.527][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `goods_comment`

[2025-08-26 10:01:48.537][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.539][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_comment' ORDER BY event_object_table

[2025-08-26 10:01:48.548][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'goods\_tags'

[2025-08-26 10:01:48.551][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `goods_tags` 

[2025-08-26 10:01:48.553][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `goods_tags`

[2025-08-26 10:01:48.556][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `goods_tags`

[2025-08-26 10:01:48.567][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'goods_tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.569][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'goods_tags' ORDER BY event_object_table

[2025-08-26 10:01:48.576][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'medal'

[2025-08-26 10:01:48.578][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `medal` 

[2025-08-26 10:01:48.579][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `medal`

[2025-08-26 10:01:48.583][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `medal`

[2025-08-26 10:01:48.585][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'medal' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.587][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'medal' ORDER BY event_object_table

[2025-08-26 10:01:48.595][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant'

[2025-08-26 10:01:48.597][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant` 

[2025-08-26 10:01:48.599][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant`

[2025-08-26 10:01:48.614][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant`

[2025-08-26 10:01:48.62][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.623][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant' ORDER BY event_object_table

[2025-08-26 10:01:48.661][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_category'

[2025-08-26 10:01:48.664][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant_category` 

[2025-08-26 10:01:48.665][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category`

[2025-08-26 10:01:48.668][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant_category`

[2025-08-26 10:01:48.671][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.672][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_category' ORDER BY event_object_table

[2025-08-26 10:01:48.679][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_category\_relation'

[2025-08-26 10:01:48.681][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant_category_relation` 

[2025-08-26 10:01:48.683][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_category_relation`

[2025-08-26 10:01:48.685][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant_category_relation`

[2025-08-26 10:01:48.688][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_category_relation' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.689][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_category_relation' ORDER BY event_object_table

[2025-08-26 10:01:48.696][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment'

[2025-08-26 10:01:48.698][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant_comment` 

[2025-08-26 10:01:48.699][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment`

[2025-08-26 10:01:48.703][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant_comment`

[2025-08-26 10:01:48.714][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.716][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment' ORDER BY event_object_table

[2025-08-26 10:01:48.724][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_comment\_like'

[2025-08-26 10:01:48.727][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant_comment_like` 

[2025-08-26 10:01:48.728][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_comment_like`

[2025-08-26 10:01:48.731][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant_comment_like`

[2025-08-26 10:01:48.733][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.735][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_comment_like' ORDER BY event_object_table

[2025-08-26 10:01:48.743][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_feature'

[2025-08-26 10:01:48.746][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant_feature` 

[2025-08-26 10:01:48.748][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_feature`

[2025-08-26 10:01:48.75][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant_feature`

[2025-08-26 10:01:48.752][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_feature' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.754][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_feature' ORDER BY event_object_table

[2025-08-26 10:01:48.76][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'merchant\_tags'

[2025-08-26 10:01:48.762][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `merchant_tags` 

[2025-08-26 10:01:48.764][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `merchant_tags`

[2025-08-26 10:01:48.766][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `merchant_tags`

[2025-08-26 10:01:48.78][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'merchant_tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.782][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'merchant_tags' ORDER BY event_object_table

[2025-08-26 10:01:48.79][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note'

[2025-08-26 10:01:48.793][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note` 

[2025-08-26 10:01:48.794][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note`

[2025-08-26 10:01:48.798][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note`

[2025-08-26 10:01:48.815][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.817][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note' ORDER BY event_object_table

[2025-08-26 10:01:48.829][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_collect'

[2025-08-26 10:01:48.832][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_collect` 

[2025-08-26 10:01:48.833][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_collect`

[2025-08-26 10:01:48.836][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_collect`

[2025-08-26 10:01:48.839][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_collect' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.84][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_collect' ORDER BY event_object_table

[2025-08-26 10:01:48.847][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment'

[2025-08-26 10:01:48.849][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_comment` 

[2025-08-26 10:01:48.85][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment`

[2025-08-26 10:01:48.854][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_comment`

[2025-08-26 10:01:48.869][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.871][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment' ORDER BY event_object_table

[2025-08-26 10:01:48.879][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_comment\_like'

[2025-08-26 10:01:48.881][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_comment_like` 

[2025-08-26 10:01:48.882][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_comment_like`

[2025-08-26 10:01:48.885][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_comment_like`

[2025-08-26 10:01:48.888][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_comment_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.889][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_comment_like' ORDER BY event_object_table

[2025-08-26 10:01:48.896][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_like'

[2025-08-26 10:01:48.898][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_like` 

[2025-08-26 10:01:48.899][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_like`

[2025-08-26 10:01:48.902][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_like`

[2025-08-26 10:01:48.904][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_like' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.906][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_like' ORDER BY event_object_table

[2025-08-26 10:01:48.913][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_merchant'

[2025-08-26 10:01:48.915][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_merchant` 

[2025-08-26 10:01:48.916][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_merchant`

[2025-08-26 10:01:48.918][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_merchant`

[2025-08-26 10:01:48.921][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_merchant' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.923][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_merchant' ORDER BY event_object_table

[2025-08-26 10:01:48.929][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_tags'

[2025-08-26 10:01:48.931][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_tags` 

[2025-08-26 10:01:48.933][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_tags`

[2025-08-26 10:01:48.935][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_tags`

[2025-08-26 10:01:48.948][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.95][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_tags' ORDER BY event_object_table

[2025-08-26 10:01:48.959][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_topic'

[2025-08-26 10:01:48.961][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_topic` 

[2025-08-26 10:01:48.962][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_topic`

[2025-08-26 10:01:48.965][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_topic`

[2025-08-26 10:01:48.967][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_topic' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.969][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_topic' ORDER BY event_object_table

[2025-08-26 10:01:48.976][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'note\_useful'

[2025-08-26 10:01:48.978][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `note_useful` 

[2025-08-26 10:01:48.979][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `note_useful`

[2025-08-26 10:01:48.982][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `note_useful`

[2025-08-26 10:01:48.984][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'note_useful' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:48.986][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'note_useful' ORDER BY event_object_table

[2025-08-26 10:01:48.993][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'poster'

[2025-08-26 10:01:48.996][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `poster` 

[2025-08-26 10:01:48.997][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `poster`

[2025-08-26 10:01:49.000][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `poster`

[2025-08-26 10:01:49.002][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'poster' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.004][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'poster' ORDER BY event_object_table

[2025-08-26 10:01:49.011][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'poster\_template'

[2025-08-26 10:01:49.013][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `poster_template` 

[2025-08-26 10:01:49.015][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `poster_template`

[2025-08-26 10:01:49.017][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `poster_template`

[2025-08-26 10:01:49.019][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'poster_template' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.021][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'poster_template' ORDER BY event_object_table

[2025-08-26 10:01:49.029][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'product'

[2025-08-26 10:01:49.031][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `product` 

[2025-08-26 10:01:49.032][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `product`

[2025-08-26 10:01:49.036][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `product`

[2025-08-26 10:01:49.039][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'product' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.041][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'product' ORDER BY event_object_table

[2025-08-26 10:01:49.051][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'product\_category'

[2025-08-26 10:01:49.055][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `product_category` 

[2025-08-26 10:01:49.056][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `product_category`

[2025-08-26 10:01:49.059][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `product_category`

[2025-08-26 10:01:49.07][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'product_category' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.073][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'product_category' ORDER BY event_object_table

[2025-08-26 10:01:49.081][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'recommend\_strategy'

[2025-08-26 10:01:49.084][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `recommend_strategy` 

[2025-08-26 10:01:49.085][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy`

[2025-08-26 10:01:49.09][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `recommend_strategy`

[2025-08-26 10:01:49.092][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'recommend_strategy' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.094][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'recommend_strategy' ORDER BY event_object_table

[2025-08-26 10:01:49.105][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'recommend\_strategy\_tag'

[2025-08-26 10:01:49.108][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `recommend_strategy_tag` 

[2025-08-26 10:01:49.109][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `recommend_strategy_tag`

[2025-08-26 10:01:49.111][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `recommend_strategy_tag`

[2025-08-26 10:01:49.114][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'recommend_strategy_tag' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.116][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'recommend_strategy_tag' ORDER BY event_object_table

[2025-08-26 10:01:49.123][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_area'

[2025-08-26 10:01:49.126][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_area` 

[2025-08-26 10:01:49.127][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_area`

[2025-08-26 10:01:49.13][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_area`

[2025-08-26 10:01:49.132][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_area' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.133][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_area' ORDER BY event_object_table

[2025-08-26 10:01:49.576][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_config'

[2025-08-26 10:01:49.578][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_config` 

[2025-08-26 10:01:49.579][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_config`

[2025-08-26 10:01:49.582][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_config`

[2025-08-26 10:01:49.584][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_config' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.586][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_config' ORDER BY event_object_table

[2025-08-26 10:01:49.594][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_dept'

[2025-08-26 10:01:49.597][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_dept` 

[2025-08-26 10:01:49.598][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dept`

[2025-08-26 10:01:49.601][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_dept`

[2025-08-26 10:01:49.603][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_dept' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.607][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_dept' ORDER BY event_object_table

[2025-08-26 10:01:49.616][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_dict\_data'

[2025-08-26 10:01:49.619][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_dict_data` 

[2025-08-26 10:01:49.62][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_data`

[2025-08-26 10:01:49.624][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_dict_data`

[2025-08-26 10:01:49.626][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_dict_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.628][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_dict_data' ORDER BY event_object_table

[2025-08-26 10:01:49.642][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_dict\_type'

[2025-08-26 10:01:49.645][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_dict_type` 

[2025-08-26 10:01:49.646][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_dict_type`

[2025-08-26 10:01:49.649][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_dict_type`

[2025-08-26 10:01:49.652][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_dict_type' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.654][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_dict_type' ORDER BY event_object_table

[2025-08-26 10:01:49.663][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_job'

[2025-08-26 10:01:49.666][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_job` 

[2025-08-26 10:01:49.667][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job`

[2025-08-26 10:01:49.673][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_job`

[2025-08-26 10:01:49.675][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_job' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.677][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_job' ORDER BY event_object_table

[2025-08-26 10:01:49.687][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_job\_log'

[2025-08-26 10:01:49.69][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_job_log` 

[2025-08-26 10:01:49.691][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_job_log`

[2025-08-26 10:01:49.694][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_job_log`

[2025-08-26 10:01:49.696][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_job_log' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.698][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_job_log' ORDER BY event_object_table

[2025-08-26 10:01:49.706][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_logininfor'

[2025-08-26 10:01:49.709][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_logininfor` 

[2025-08-26 10:01:49.711][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_logininfor`

[2025-08-26 10:01:49.714][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_logininfor`

[2025-08-26 10:01:49.716][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_logininfor' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.718][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_logininfor' ORDER BY event_object_table

[2025-08-26 10:01:49.728][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_menu'

[2025-08-26 10:01:49.731][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_menu` 

[2025-08-26 10:01:49.732][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_menu`

[2025-08-26 10:01:49.737][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_menu`

[2025-08-26 10:01:49.739][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_menu' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.741][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_menu' ORDER BY event_object_table

[2025-08-26 10:01:49.762][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_notice'

[2025-08-26 10:01:49.765][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_notice` 

[2025-08-26 10:01:49.766][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_notice`

[2025-08-26 10:01:49.769][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_notice`

[2025-08-26 10:01:49.773][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_notice' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.775][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_notice' ORDER BY event_object_table

[2025-08-26 10:01:49.784][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_oper\_log'

[2025-08-26 10:01:49.787][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_oper_log` 

[2025-08-26 10:01:49.789][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_oper_log`

[2025-08-26 10:01:49.793][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_oper_log`

[2025-08-26 10:01:49.796][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_oper_log' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.797][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_oper_log' ORDER BY event_object_table

[2025-08-26 10:01:49.83][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_post'

[2025-08-26 10:01:49.833][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_post` 

[2025-08-26 10:01:49.834][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_post`

[2025-08-26 10:01:49.84][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_post`

[2025-08-26 10:01:49.842][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_post' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.844][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_post' ORDER BY event_object_table

[2025-08-26 10:01:49.853][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_role'

[2025-08-26 10:01:49.856][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_role` 

[2025-08-26 10:01:49.858][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role`

[2025-08-26 10:01:49.862][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_role`

[2025-08-26 10:01:49.864][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_role' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.865][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_role' ORDER BY event_object_table

[2025-08-26 10:01:49.875][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_role\_dept'

[2025-08-26 10:01:49.877][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_role_dept` 

[2025-08-26 10:01:49.878][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_dept`

[2025-08-26 10:01:49.88][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_role_dept`

[2025-08-26 10:01:49.882][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_role_dept' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.883][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_role_dept' ORDER BY event_object_table

[2025-08-26 10:01:49.89][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_role\_menu'

[2025-08-26 10:01:49.892][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_role_menu` 

[2025-08-26 10:01:49.893][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_role_menu`

[2025-08-26 10:01:49.895][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_role_menu`

[2025-08-26 10:01:49.897][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_role_menu' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.898][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_role_menu' ORDER BY event_object_table

[2025-08-26 10:01:49.905][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_user'

[2025-08-26 10:01:49.908][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_user` 

[2025-08-26 10:01:49.909][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user`

[2025-08-26 10:01:49.913][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_user`

[2025-08-26 10:01:49.915][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_user' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.917][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_user' ORDER BY event_object_table

[2025-08-26 10:01:49.929][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_user\_post'

[2025-08-26 10:01:49.931][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_user_post` 

[2025-08-26 10:01:49.932][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_post`

[2025-08-26 10:01:49.934][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_user_post`

[2025-08-26 10:01:49.936][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_user_post' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.938][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_user_post' ORDER BY event_object_table

[2025-08-26 10:01:49.944][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'sys\_user\_role'

[2025-08-26 10:01:49.946][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `sys_user_role` 

[2025-08-26 10:01:49.947][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `sys_user_role`

[2025-08-26 10:01:49.949][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `sys_user_role`

[2025-08-26 10:01:49.951][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'sys_user_role' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.953][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'sys_user_role' ORDER BY event_object_table

[2025-08-26 10:01:49.961][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'tags'

[2025-08-26 10:01:49.963][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `tags` 

[2025-08-26 10:01:49.964][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `tags`

[2025-08-26 10:01:49.967][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `tags`

[2025-08-26 10:01:49.969][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'tags' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.972][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'tags' ORDER BY event_object_table

[2025-08-26 10:01:49.978][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'task'

[2025-08-26 10:01:49.981][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `task` 

[2025-08-26 10:01:49.982][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `task`

[2025-08-26 10:01:49.985][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `task`

[2025-08-26 10:01:49.988][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'task' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:49.99][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'task' ORDER BY event_object_table

[2025-08-26 10:01:49.999][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_asset'

[2025-08-26 10:01:50.002][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_asset` 

[2025-08-26 10:01:50.003][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_asset`

[2025-08-26 10:01:50.006][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_asset`

[2025-08-26 10:01:50.009][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_asset' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.011][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_asset' ORDER BY event_object_table

[2025-08-26 10:01:50.021][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_follow'

[2025-08-26 10:01:50.023][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_follow` 

[2025-08-26 10:01:50.024][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_follow`

[2025-08-26 10:01:50.026][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_follow`

[2025-08-26 10:01:50.029][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_follow' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.031][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_follow' ORDER BY event_object_table

[2025-08-26 10:01:50.038][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_level'

[2025-08-26 10:01:50.04][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_level` 

[2025-08-26 10:01:50.042][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_level`

[2025-08-26 10:01:50.045][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_level`

[2025-08-26 10:01:50.047][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_level' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.049][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_level' ORDER BY event_object_table

[2025-08-26 10:01:50.057][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_medal'

[2025-08-26 10:01:50.06][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_medal` 

[2025-08-26 10:01:50.061][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_medal`

[2025-08-26 10:01:50.064][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_medal`

[2025-08-26 10:01:50.066][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_medal' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.068][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_medal' ORDER BY event_object_table

[2025-08-26 10:01:50.077][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_recharge\_record'

[2025-08-26 10:01:50.08][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_recharge_record` 

[2025-08-26 10:01:50.082][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_recharge_record`

[2025-08-26 10:01:50.084][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_recharge_record`

[2025-08-26 10:01:50.087][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_recharge_record' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.089][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_recharge_record' ORDER BY event_object_table

[2025-08-26 10:01:50.097][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_task\_completion'

[2025-08-26 10:01:50.099][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_task_completion` 

[2025-08-26 10:01:50.1][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_task_completion`

[2025-08-26 10:01:50.103][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_task_completion`

[2025-08-26 10:01:50.106][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_task_completion' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.108][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_task_completion' ORDER BY event_object_table

[2025-08-26 10:01:50.117][本地Mysql8][000646][MYSQL][]
SHOW TABLE STATUS LIKE 'user\_vip'

[2025-08-26 10:01:50.119][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `user_vip` 

[2025-08-26 10:01:50.12][本地Mysql8][000646][MYSQL][]
SHOW FULL COLUMNS FROM `user_vip`

[2025-08-26 10:01:50.123][本地Mysql8][000646][MYSQL][]
SHOW INDEX FROM `user_vip`

[2025-08-26 10:01:50.126][本地Mysql8][000646][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'izhule' AND TABLE_NAME LIKE BINARY 'user_vip' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 10:01:50.128][本地Mysql8][000646][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'izhule' AND event_object_table = BINARY 'user_vip' ORDER BY event_object_table

[2025-08-26 10:09:51.974][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:09:51.984][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:10:04.18][本地Mysql8][000645][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`agent`, `izhule`.`agent_application`, `izhule`.`agent_commission_record`, `izhule`.`asset_exchange`, `izhule`.`banner`, `izhule`.`city`, `izhule`.`comment_like`, `izhule`.`flyway_schema_history`, `izhule`.`fun`, `izhule`.`fun_cart`, `izhule`.`fun_comment`, `izhule`.`fun_comment_like`, `izhule`.`fun_merchant`, `izhule`.`fun_order`, `izhule`.`fun_order_redemption`, `izhule`.`fun_rule`, `izhule`.`fun_tags`, `izhule`.`gen_table`, `izhule`.`gen_table_column`, `izhule`.`goods`, `izhule`.`goods_category`, `izhule`.`goods_comment`, `izhule`.`goods_tags`, `izhule`.`medal`, `izhule`.`merchant`, `izhule`.`merchant_category`, `izhule`.`merchant_category_relation`, `izhule`.`merchant_comment`, `izhule`.`merchant_comment_like`, `izhule`.`merchant_feature`, `izhule`.`merchant_tags`, `izhule`.`note`, `izhule`.`note_collect`, `izhule`.`note_comment`, `izhule`.`note_comment_like`, `izhule`.`note_like`, `izhule`.`note_merchant`, `izhule`.`note_tags`, `izhule`.`note_topic`, `izhule`.`note_useful`, `izhule`.`poster`, `izhule`.`poster_template`, `izhule`.`product`, `izhule`.`product_category`, `izhule`.`QRTZ_BLOB_TRIGGERS`, `izhule`.`QRTZ_CALENDARS`, `izhule`.`QRTZ_CRON_TRIGGERS`, `izhule`.`QRTZ_FIRED_TRIGGERS`, `izhule`.`QRTZ_JOB_DETAILS`, `izhule`.`QRTZ_LOCKS`, `izhule`.`QRTZ_PAUSED_TRIGGER_GRPS`, `izhule`.`QRTZ_SCHEDULER_STATE`, `izhule`.`QRTZ_SIMPLE_TRIGGERS`, `izhule`.`QRTZ_SIMPROP_TRIGGERS`, `izhule`.`QRTZ_TRIGGERS`, `izhule`.`recommend_strategy`, `izhule`.`recommend_strategy_tag`, `izhule`.`sys_area`, `izhule`.`sys_config`, `izhule`.`sys_dept`, `izhule`.`sys_dict_data`, `izhule`.`sys_dict_type`, `izhule`.`sys_job`, `izhule`.`sys_job_log`, `izhule`.`sys_logininfor`, `izhule`.`sys_menu`, `izhule`.`sys_notice`, `izhule`.`sys_oper_log`, `izhule`.`sys_post`, `izhule`.`sys_role`, `izhule`.`sys_role_dept`, `izhule`.`sys_role_menu`, `izhule`.`sys_user`, `izhule`.`sys_user_post`, `izhule`.`sys_user_role`, `izhule`.`tags`, `izhule`.`task`, `izhule`.`user_asset`, `izhule`.`user_follow`, `izhule`.`user_level`, `izhule`.`user_medal`, `izhule`.`user_recharge_record`, `izhule`.`user_task_completion`, `izhule`.`user_vip`;

[2025-08-26 10:10:06.679][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:10:06.682][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:15:04.839][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:15:04.844][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:15:12.509][本地Mysql8][000645][MYSQL][]
DELETE FROM `agent`

[2025-08-26 10:15:12.512][本地Mysql8][000645][MYSQL][]
DELETE FROM `agent_application`

[2025-08-26 10:15:12.515][本地Mysql8][000645][MYSQL][]
DELETE FROM `agent_commission_record`

[2025-08-26 10:15:12.516][本地Mysql8][000645][MYSQL][]
DELETE FROM `asset_exchange`

[2025-08-26 10:15:12.518][本地Mysql8][000645][MYSQL][]
DELETE FROM `banner`

[2025-08-26 10:15:12.521][本地Mysql8][000645][MYSQL][]
DELETE FROM `city`

[2025-08-26 10:15:12.522][本地Mysql8][000645][MYSQL][]
DELETE FROM `comment_like`

[2025-08-26 10:15:12.523][本地Mysql8][000645][MYSQL][]
DELETE FROM `flyway_schema_history`

[2025-08-26 10:15:12.528][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun`

[2025-08-26 10:15:12.531][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_cart`

[2025-08-26 10:15:12.532][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_comment`

[2025-08-26 10:15:12.534][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_comment_like`

[2025-08-26 10:15:12.535][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_merchant`

[2025-08-26 10:15:12.54][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_order`

[2025-08-26 10:15:12.542][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_order_redemption`

[2025-08-26 10:15:12.543][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_rule`

[2025-08-26 10:15:12.545][本地Mysql8][000645][MYSQL][]
DELETE FROM `fun_tags`

[2025-08-26 10:15:12.546][本地Mysql8][000645][MYSQL][]
DELETE FROM `gen_table`

[2025-08-26 10:15:12.551][本地Mysql8][000645][MYSQL][]
DELETE FROM `gen_table_column`

[2025-08-26 10:15:12.555][本地Mysql8][000645][MYSQL][]
DELETE FROM `goods`

[2025-08-26 10:15:12.56][本地Mysql8][000645][MYSQL][]
DELETE FROM `goods_category`

[2025-08-26 10:15:12.563][本地Mysql8][000645][MYSQL][]
DELETE FROM `goods_comment`

[2025-08-26 10:15:12.565][本地Mysql8][000645][MYSQL][]
DELETE FROM `goods_tags`

[2025-08-26 10:15:12.566][本地Mysql8][000645][MYSQL][]
DELETE FROM `medal`

[2025-08-26 10:15:12.568][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant`

[2025-08-26 10:15:12.573][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant_category`

[2025-08-26 10:15:12.576][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant_category_relation`

[2025-08-26 10:15:12.578][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant_comment`

[2025-08-26 10:15:12.58][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant_comment_like`

[2025-08-26 10:15:12.581][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant_feature`

[2025-08-26 10:15:12.582][本地Mysql8][000645][MYSQL][]
DELETE FROM `merchant_tags`

[2025-08-26 10:15:12.584][本地Mysql8][000645][MYSQL][]
DELETE FROM `note`

[2025-08-26 10:15:12.587][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_collect`

[2025-08-26 10:15:12.588][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_comment`

[2025-08-26 10:15:12.592][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_comment_like`

[2025-08-26 10:15:12.593][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_like`

[2025-08-26 10:15:12.599][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_merchant`

[2025-08-26 10:15:12.601][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_tags`

[2025-08-26 10:15:12.603][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_topic`

[2025-08-26 10:15:12.605][本地Mysql8][000645][MYSQL][]
DELETE FROM `note_useful`

[2025-08-26 10:15:12.607][本地Mysql8][000645][MYSQL][]
DELETE FROM `poster`

[2025-08-26 10:15:12.608][本地Mysql8][000645][MYSQL][]
DELETE FROM `poster_template`

[2025-08-26 10:15:12.61][本地Mysql8][000645][MYSQL][]
DELETE FROM `product`

[2025-08-26 10:15:12.613][本地Mysql8][000645][MYSQL][]
DELETE FROM `product_category`

[2025-08-26 10:15:12.616][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_BLOB_TRIGGERS`

[2025-08-26 10:15:12.618][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_CALENDARS`

[2025-08-26 10:15:12.619][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_CRON_TRIGGERS`

[2025-08-26 10:15:12.621][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_FIRED_TRIGGERS`

[2025-08-26 10:15:12.623][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_JOB_DETAILS`

[2025-08-26 10:15:12.624][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_LOCKS`

[2025-08-26 10:15:12.625][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_PAUSED_TRIGGER_GRPS`

[2025-08-26 10:15:12.626][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_SCHEDULER_STATE`

[2025-08-26 10:15:12.627][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_SIMPLE_TRIGGERS`

[2025-08-26 10:15:12.628][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_SIMPROP_TRIGGERS`

[2025-08-26 10:15:12.63][本地Mysql8][000645][MYSQL][]
DELETE FROM `QRTZ_TRIGGERS`

[2025-08-26 10:15:12.632][本地Mysql8][000645][MYSQL][]
DELETE FROM `recommend_strategy`

[2025-08-26 10:15:12.634][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:15:14.462][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:15:14.467][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:15:22.684][本地Mysql8][000645][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`agent`, `izhule`.`agent_application`, `izhule`.`agent_commission_record`, `izhule`.`asset_exchange`, `izhule`.`banner`, `izhule`.`city`, `izhule`.`comment_like`, `izhule`.`flyway_schema_history`, `izhule`.`fun`, `izhule`.`fun_cart`, `izhule`.`fun_comment`, `izhule`.`fun_comment_like`, `izhule`.`fun_merchant`, `izhule`.`fun_order`, `izhule`.`fun_order_redemption`, `izhule`.`fun_rule`, `izhule`.`fun_tags`, `izhule`.`gen_table`, `izhule`.`gen_table_column`, `izhule`.`goods`, `izhule`.`goods_category`, `izhule`.`goods_comment`, `izhule`.`goods_tags`, `izhule`.`medal`, `izhule`.`merchant`, `izhule`.`merchant_category`, `izhule`.`merchant_category_relation`, `izhule`.`merchant_comment`, `izhule`.`merchant_comment_like`, `izhule`.`merchant_feature`, `izhule`.`merchant_tags`, `izhule`.`note`, `izhule`.`note_collect`, `izhule`.`note_comment`, `izhule`.`note_comment_like`, `izhule`.`note_like`, `izhule`.`note_merchant`, `izhule`.`note_tags`, `izhule`.`note_topic`, `izhule`.`note_useful`, `izhule`.`poster`, `izhule`.`poster_template`, `izhule`.`product`, `izhule`.`product_category`, `izhule`.`QRTZ_BLOB_TRIGGERS`, `izhule`.`QRTZ_CALENDARS`, `izhule`.`QRTZ_CRON_TRIGGERS`, `izhule`.`QRTZ_FIRED_TRIGGERS`, `izhule`.`QRTZ_JOB_DETAILS`, `izhule`.`QRTZ_LOCKS`, `izhule`.`QRTZ_PAUSED_TRIGGER_GRPS`, `izhule`.`QRTZ_SCHEDULER_STATE`, `izhule`.`QRTZ_SIMPLE_TRIGGERS`, `izhule`.`QRTZ_SIMPROP_TRIGGERS`, `izhule`.`QRTZ_TRIGGERS`, `izhule`.`recommend_strategy`;

[2025-08-26 10:16:17.966][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:16:17.969][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:20:02.35][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:20:02.356][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:21:21.15][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:21:21.156][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:21:33.888][本地Mysql8][000645][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`agent`, `izhule`.`agent_application`, `izhule`.`agent_commission_record`, `izhule`.`asset_exchange`, `izhule`.`banner`, `izhule`.`city`, `izhule`.`comment_like`, `izhule`.`flyway_schema_history`, `izhule`.`fun`, `izhule`.`fun_cart`, `izhule`.`fun_comment`, `izhule`.`fun_comment_like`, `izhule`.`fun_merchant`, `izhule`.`fun_order`, `izhule`.`fun_order_redemption`, `izhule`.`fun_rule`, `izhule`.`fun_tags`, `izhule`.`gen_table`, `izhule`.`gen_table_column`, `izhule`.`goods`, `izhule`.`goods_category`, `izhule`.`goods_comment`, `izhule`.`goods_tags`, `izhule`.`medal`, `izhule`.`merchant`, `izhule`.`merchant_category`, `izhule`.`merchant_category_relation`, `izhule`.`merchant_comment`, `izhule`.`merchant_comment_like`, `izhule`.`merchant_feature`, `izhule`.`merchant_tags`, `izhule`.`note`, `izhule`.`note_collect`, `izhule`.`note_comment`, `izhule`.`note_comment_like`, `izhule`.`note_like`, `izhule`.`note_merchant`, `izhule`.`note_tags`, `izhule`.`note_topic`, `izhule`.`note_useful`, `izhule`.`poster`, `izhule`.`poster_template`, `izhule`.`product`, `izhule`.`product_category`, `izhule`.`QRTZ_BLOB_TRIGGERS`, `izhule`.`QRTZ_CALENDARS`, `izhule`.`QRTZ_CRON_TRIGGERS`, `izhule`.`QRTZ_FIRED_TRIGGERS`, `izhule`.`QRTZ_JOB_DETAILS`, `izhule`.`QRTZ_LOCKS`, `izhule`.`QRTZ_PAUSED_TRIGGER_GRPS`, `izhule`.`QRTZ_SCHEDULER_STATE`, `izhule`.`QRTZ_SIMPLE_TRIGGERS`, `izhule`.`QRTZ_SIMPROP_TRIGGERS`, `izhule`.`QRTZ_TRIGGERS`, `izhule`.`recommend_strategy`, `izhule`.`recommend_strategy_tag`, `izhule`.`sys_area`, `izhule`.`sys_config`, `izhule`.`sys_dept`, `izhule`.`sys_dict_data`, `izhule`.`sys_dict_type`, `izhule`.`sys_job`, `izhule`.`sys_job_log`, `izhule`.`sys_logininfor`, `izhule`.`sys_menu`, `izhule`.`sys_notice`, `izhule`.`sys_oper_log`, `izhule`.`sys_post`, `izhule`.`sys_role`, `izhule`.`sys_role_dept`, `izhule`.`sys_role_menu`, `izhule`.`sys_user`, `izhule`.`sys_user_post`, `izhule`.`sys_user_role`, `izhule`.`tags`, `izhule`.`task`, `izhule`.`user_asset`, `izhule`.`user_follow`, `izhule`.`user_level`, `izhule`.`user_medal`, `izhule`.`user_recharge_record`, `izhule`.`user_task_completion`, `izhule`.`user_vip`;

[2025-08-26 10:21:36.246][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:21:36.248][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:21:37.782][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:21:37.785][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:25:51.439][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:25:51.448][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:25:56.573][本地Mysql8][000645][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`agent`, `izhule`.`agent_application`, `izhule`.`agent_commission_record`, `izhule`.`asset_exchange`, `izhule`.`banner`, `izhule`.`city`, `izhule`.`comment_like`, `izhule`.`flyway_schema_history`, `izhule`.`fun`, `izhule`.`fun_cart`, `izhule`.`fun_comment`, `izhule`.`fun_comment_like`, `izhule`.`fun_merchant`, `izhule`.`fun_order`, `izhule`.`fun_order_redemption`, `izhule`.`fun_rule`, `izhule`.`fun_tags`, `izhule`.`gen_table`, `izhule`.`gen_table_column`, `izhule`.`goods`, `izhule`.`goods_category`, `izhule`.`goods_comment`, `izhule`.`goods_tags`, `izhule`.`medal`, `izhule`.`merchant`, `izhule`.`merchant_category`, `izhule`.`merchant_category_relation`, `izhule`.`merchant_comment`, `izhule`.`merchant_comment_like`, `izhule`.`merchant_feature`, `izhule`.`merchant_tags`, `izhule`.`note`, `izhule`.`note_collect`, `izhule`.`note_comment`, `izhule`.`note_comment_like`, `izhule`.`note_like`, `izhule`.`note_merchant`, `izhule`.`note_tags`, `izhule`.`note_topic`, `izhule`.`note_useful`, `izhule`.`poster`, `izhule`.`poster_template`, `izhule`.`product`, `izhule`.`product_category`, `izhule`.`QRTZ_BLOB_TRIGGERS`, `izhule`.`QRTZ_CALENDARS`, `izhule`.`QRTZ_CRON_TRIGGERS`, `izhule`.`QRTZ_FIRED_TRIGGERS`, `izhule`.`QRTZ_JOB_DETAILS`, `izhule`.`QRTZ_LOCKS`, `izhule`.`QRTZ_PAUSED_TRIGGER_GRPS`, `izhule`.`QRTZ_SCHEDULER_STATE`, `izhule`.`QRTZ_SIMPLE_TRIGGERS`, `izhule`.`QRTZ_SIMPROP_TRIGGERS`, `izhule`.`QRTZ_TRIGGERS`, `izhule`.`recommend_strategy`, `izhule`.`recommend_strategy_tag`, `izhule`.`sys_area`, `izhule`.`sys_config`, `izhule`.`sys_dept`, `izhule`.`sys_dict_data`, `izhule`.`sys_dict_type`, `izhule`.`sys_job`, `izhule`.`sys_job_log`, `izhule`.`sys_logininfor`, `izhule`.`sys_menu`, `izhule`.`sys_notice`, `izhule`.`sys_oper_log`, `izhule`.`sys_post`, `izhule`.`sys_role`, `izhule`.`sys_role_dept`, `izhule`.`sys_role_menu`, `izhule`.`sys_user`, `izhule`.`sys_user_post`, `izhule`.`sys_user_role`, `izhule`.`tags`, `izhule`.`task`, `izhule`.`user_asset`, `izhule`.`user_follow`, `izhule`.`user_level`, `izhule`.`user_medal`, `izhule`.`user_recharge_record`, `izhule`.`user_task_completion`, `izhule`.`user_vip`;

[2025-08-26 10:26:21.976][本地Mysql8][000645][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 10:26:21.989][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 10:26:24.015][本地Mysql8][000645][MYSQL][]
SELECT * FROM `izhule`.`flyway_schema_history` LIMIT 0,1000

[2025-08-26 10:26:24.016][本地Mysql8][000645][MYSQL][]
SHOW TABLE STATUS LIKE 'flyway_schema_history'

[2025-08-26 10:26:24.02][本地Mysql8][000652][MYSQL][]
SHOW COLUMNS FROM `izhule`.`flyway_schema_history`

[2025-08-26 10:26:24.022][本地Mysql8][000646][MYSQL][]
SHOW CREATE TABLE `izhule`.`flyway_schema_history`

[2025-08-26 10:26:24.024][本地Mysql8][000652][MYSQL][]
SHOW INDEX FROM `flyway_schema_history`

[2025-08-26 10:26:24.031][本地Mysql8][000645][MYSQL][]
SHOW CREATE TABLE `flyway_schema_history` 

[2025-08-26 10:26:24.032][本地Mysql8][000645][MYSQL][]
SHOW FULL COLUMNS FROM `flyway_schema_history`

[2025-08-26 10:27:15.294][本地Mysql8][000645][MYSQL][]
DROP TABLE IF EXISTS `izhule`.`agent`, `izhule`.`agent_application`, `izhule`.`agent_commission_record`, `izhule`.`asset_exchange`, `izhule`.`banner`, `izhule`.`city`, `izhule`.`comment_like`, `izhule`.`flyway_schema_history`, `izhule`.`fun`, `izhule`.`fun_cart`, `izhule`.`fun_comment`, `izhule`.`fun_comment_like`, `izhule`.`fun_merchant`, `izhule`.`fun_order`, `izhule`.`fun_order_redemption`, `izhule`.`fun_rule`, `izhule`.`fun_tags`, `izhule`.`gen_table`, `izhule`.`gen_table_column`, `izhule`.`goods`, `izhule`.`goods_category`, `izhule`.`goods_comment`, `izhule`.`goods_tags`, `izhule`.`medal`, `izhule`.`merchant`, `izhule`.`merchant_category`, `izhule`.`merchant_category_relation`, `izhule`.`merchant_comment`, `izhule`.`merchant_comment_like`, `izhule`.`merchant_feature`, `izhule`.`merchant_tags`, `izhule`.`note`, `izhule`.`note_collect`, `izhule`.`note_comment`, `izhule`.`note_comment_like`, `izhule`.`note_like`, `izhule`.`note_merchant`, `izhule`.`note_tags`, `izhule`.`note_topic`, `izhule`.`note_useful`, `izhule`.`poster`, `izhule`.`poster_template`, `izhule`.`product`, `izhule`.`product_category`, `izhule`.`QRTZ_BLOB_TRIGGERS`, `izhule`.`QRTZ_CALENDARS`, `izhule`.`QRTZ_CRON_TRIGGERS`, `izhule`.`QRTZ_FIRED_TRIGGERS`, `izhule`.`QRTZ_JOB_DETAILS`, `izhule`.`QRTZ_LOCKS`, `izhule`.`QRTZ_PAUSED_TRIGGER_GRPS`, `izhule`.`QRTZ_SCHEDULER_STATE`, `izhule`.`QRTZ_SIMPLE_TRIGGERS`, `izhule`.`QRTZ_SIMPROP_TRIGGERS`, `izhule`.`QRTZ_TRIGGERS`, `izhule`.`recommend_strategy`, `izhule`.`recommend_strategy_tag`, `izhule`.`sys_area`, `izhule`.`sys_config`, `izhule`.`sys_dept`, `izhule`.`sys_dict_data`, `izhule`.`sys_dict_type`, `izhule`.`sys_job`, `izhule`.`sys_job_log`, `izhule`.`sys_logininfor`, `izhule`.`sys_menu`, `izhule`.`sys_notice`, `izhule`.`sys_oper_log`, `izhule`.`sys_post`, `izhule`.`sys_role`, `izhule`.`sys_role_dept`, `izhule`.`sys_role_menu`, `izhule`.`sys_user`, `izhule`.`sys_user_post`, `izhule`.`sys_user_role`, `izhule`.`tags`, `izhule`.`task`, `izhule`.`user_asset`, `izhule`.`user_follow`, `izhule`.`user_level`, `izhule`.`user_medal`, `izhule`.`user_recharge_record`, `izhule`.`user_task_completion`, `izhule`.`user_vip`;

[2025-08-26 11:19:26.372][本地3306 ***********][000007][MYSQL][]
SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'adventureworkslt2022' UNION SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'adventureworkslt2022' UNION SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'adventureworkslt2022'

[2025-08-26 11:19:26.389][本地3306 ***********][000008][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 11:19:26.4][本地3306 ***********][000008][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'adventureworkslt2022' ORDER BY TABLE_SCHEMA, TABLE_TYPE

[2025-08-26 11:19:26.4][本地3306 ***********][000007][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 11:19:26.408][本地3306 ***********][000008][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'adventureworkslt2022' ORDER BY TABLE_SCHEMA, TABLE_NAME

[2025-08-26 11:19:26.414][本地3306 ***********][000008][MYSQL][]
SELECT DISTINCT ROUTINE_SCHEMA, ROUTINE_NAME, PARAMS.PARAMETER FROM information_schema.ROUTINES LEFT JOIN ( SELECT SPECIFIC_SCHEMA, SPECIFIC_NAME, GROUP_CONCAT(CONCAT(DATA_TYPE, ' ', PARAMETER_NAME) ORDER BY ORDINAL_POSITION SEPARATOR ', ') PARAMETER, ROUTINE_TYPE FROM information_schema.PARAMETERS GROUP BY SPECIFIC_SCHEMA, SPECIFIC_NAME, ROUTINE_TYPE ) PARAMS ON ROUTINES.ROUTINE_SCHEMA = PARAMS.SPECIFIC_SCHEMA AND ROUTINES.ROUTINE_NAME = PARAMS.SPECIFIC_NAME AND ROUTINES.ROUTINE_TYPE = PARAMS.ROUTINE_TYPE WHERE ROUTINE_SCHEMA = 'adventureworkslt2022' ORDER BY ROUTINE_SCHEMA

[2025-08-26 11:19:39.857][本地3306 ***********][000007][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 11:19:39.86][本地3306 ***********][000007][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 11:19:53.094][阿里云   保旅 **************  3306][003565][MYSQL][]
SHOW VARIABLES LIKE 'lower_case_%'; SHOW VARIABLES LIKE 'sql_mode'; SELECT COUNT(*) AS support_ndb FROM information_schema.ENGINES WHERE Engine = 'ndbcluster'

[2025-08-26 11:19:53.106][阿里云   保旅 **************  3306][003565][MYSQL][]
SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA

[2025-08-26 11:19:54.64][阿里云   保旅 **************  3306][003565][MYSQL][]
SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'adventureworkslt2022' UNION SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'adventureworkslt2022' UNION SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'adventureworkslt2022'

[2025-08-26 11:19:54.653][阿里云   保旅 **************  3306][003565][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'adventureworkslt2022' ORDER BY TABLE_SCHEMA, TABLE_TYPE

[2025-08-26 11:19:54.659][阿里云   保旅 **************  3306][003565][MYSQL][]
SELECT TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'adventureworkslt2022' ORDER BY TABLE_SCHEMA, TABLE_NAME

[2025-08-26 11:19:54.675][阿里云   保旅 **************  3306][003565][MYSQL][]
SELECT DISTINCT ROUTINE_SCHEMA, ROUTINE_NAME, PARAMS.PARAMETER FROM information_schema.ROUTINES LEFT JOIN ( SELECT SPECIFIC_SCHEMA, SPECIFIC_NAME, GROUP_CONCAT(CONCAT(DATA_TYPE, ' ', PARAMETER_NAME) ORDER BY ORDINAL_POSITION SEPARATOR ', ') PARAMETER, ROUTINE_TYPE FROM information_schema.PARAMETERS GROUP BY SPECIFIC_SCHEMA, SPECIFIC_NAME, ROUTINE_TYPE ) PARAMS ON ROUTINES.ROUTINE_SCHEMA = PARAMS.SPECIFIC_SCHEMA AND ROUTINES.ROUTINE_NAME = PARAMS.SPECIFIC_NAME AND ROUTINES.ROUTINE_TYPE = PARAMS.ROUTINE_TYPE WHERE ROUTINE_SCHEMA = 'adventureworkslt2022' ORDER BY ROUTINE_SCHEMA

[2025-08-26 11:19:54.702][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 11:19:54.724][阿里云   保旅 **************  3306][003565][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 11:20:03.676][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS

[2025-08-26 11:20:03.701][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, CHECK_OPTION, IS_UPDATABLE, SECURITY_TYPE, DEFINER FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'adventureworkslt2022' ORDER BY TABLE_NAME ASC

[2025-08-26 11:20:03.719][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW PROCEDURE STATUS WHERE Db = 'adventureworkslt2022'

[2025-08-26 11:20:03.728][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FUNCTION STATUS WHERE Db = 'adventureworkslt2022'

[2025-08-26 11:20:03.738][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT * FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'adventureworkslt2022' ORDER BY ROUTINE_NAME

[2025-08-26 11:20:03.756][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT EVENT_CATALOG, EVENT_SCHEMA, EVENT_NAME, DEFINER, TIME_ZONE, EVENT_DEFINITION, EVENT_BODY, EVENT_TYPE, SQL_MODE, STATUS, EXECUTE_AT, INTERVAL_VALUE, INTERVAL_FIELD, STARTS, ENDS, ON_COMPLETION, CREATED, LAST_ALTERED, LAST_EXECUTED, ORIGINATOR, CHARACTER_SET_CLIENT, COLLATION_CONNECTION, DATABASE_COLLATION, EVENT_COMMENT FROM information_schema.EVENTS WHERE EVENT_SCHEMA = 'adventureworkslt2022' ORDER BY EVENT_NAME ASC

[2025-08-26 11:20:03.775][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Customer\_data'

[2025-08-26 11:20:03.79][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Customer_data` 

[2025-08-26 11:20:03.804][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Customer_data`

[2025-08-26 11:20:03.826][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Customer_data`

[2025-08-26 11:20:03.84][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Customer_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:03.855][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Customer_data' ORDER BY event_object_table

[2025-08-26 11:20:07.08][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Date\_data'

[2025-08-26 11:20:07.095][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Date_data` 

[2025-08-26 11:20:07.11][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Date_data`

[2025-08-26 11:20:07.126][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Date_data`

[2025-08-26 11:20:07.142][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Date_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:07.157][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Date_data' ORDER BY event_object_table

[2025-08-26 11:20:07.29][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Product\_data'

[2025-08-26 11:20:07.304][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Product_data` 

[2025-08-26 11:20:07.32][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Product_data`

[2025-08-26 11:20:07.336][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Product_data`

[2025-08-26 11:20:07.35][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Product_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:07.366][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Product_data' ORDER BY event_object_table

[2025-08-26 11:20:07.442][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Reseller\_data'

[2025-08-26 11:20:07.465][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Reseller_data` 

[2025-08-26 11:20:07.479][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Reseller_data`

[2025-08-26 11:20:07.498][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Reseller_data`

[2025-08-26 11:20:07.513][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Reseller_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:07.529][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Reseller_data' ORDER BY event_object_table

[2025-08-26 11:20:07.639][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Sales Order\_data'

[2025-08-26 11:20:07.653][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Sales Order_data` 

[2025-08-26 11:20:07.667][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Sales Order_data`

[2025-08-26 11:20:07.681][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Sales Order_data`

[2025-08-26 11:20:07.695][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Sales Order_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:07.709][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Sales Order_data' ORDER BY event_object_table

[2025-08-26 11:20:19.076][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Sales Territory\_data'

[2025-08-26 11:20:19.096][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Sales Territory_data` 

[2025-08-26 11:20:19.124][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Sales Territory_data`

[2025-08-26 11:20:19.141][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Sales Territory_data`

[2025-08-26 11:20:19.156][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Sales Territory_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:19.172][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Sales Territory_data' ORDER BY event_object_table

[2025-08-26 11:20:19.203][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'Sales\_data'

[2025-08-26 11:20:19.226][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `Sales_data` 

[2025-08-26 11:20:19.241][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `Sales_data`

[2025-08-26 11:20:19.257][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `Sales_data`

[2025-08-26 11:20:19.277][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'Sales_data' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:19.298][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'Sales_data' ORDER BY event_object_table

[2025-08-26 11:20:43.122][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW TABLE STATUS LIKE 'saleslt\_address'

[2025-08-26 11:20:43.137][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW CREATE TABLE `saleslt_address` 

[2025-08-26 11:20:43.152][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW FULL COLUMNS FROM `saleslt_address`

[2025-08-26 11:20:43.166][阿里云   保旅 **************  3306][003566][MYSQL][]
SHOW INDEX FROM `saleslt_address`

[2025-08-26 11:20:43.18][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT TABLE_NAME, PARTITION_NAME, SUBPARTITION_NAME, PARTITION_METHOD, SUBPARTITION_METHOD, PARTITION_EXPRESSION, SUBPARTITION_EXPRESSION, PARTITION_DESCRIPTION, PARTITION_COMMENT, NODEGROUP, TABLESPACE_NAME FROM information_schema.PARTITIONS WHERE NOT ISNULL(PARTITION_NAME) AND TABLE_SCHEMA LIKE BINARY 'adventureworkslt2022' AND TABLE_NAME LIKE BINARY 'saleslt_address' ORDER BY TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION, SUBPARTITION_ORDINAL_POSITION

[2025-08-26 11:20:43.194][阿里云   保旅 **************  3306][003566][MYSQL][]
SELECT ACTION_ORDER, EVENT_OBJECT_TABLE, TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, DEFINER, ACTION_STATEMENT, ACTION_TIMING FROM information_schema.triggers WHERE event_object_schema = BINARY 'adventureworkslt2022' AND event_object_table = BINARY 'saleslt_address' ORDER BY event_object_table

[2025-08-26 11:31:14.933][阿里云   保旅 **************  3306][003567][MYSQL][]
SHOW FULL TABLES WHERE Table_type != 'VIEW'

[2025-08-26 11:31:14.946][阿里云   保旅 **************  3306][003567][MYSQL][]
SHOW TABLE STATUS

