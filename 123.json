{"log": {"loglevel": "warning"}, "dns": {"hosts": {"dns.google": "*******", "proxy.example.com": "127.0.0.1"}, "servers": [{"address": "*******", "skipFallback": true, "domains": ["domain:googleapis.cn", "domain:gstatic.com"]}, {"address": "*********", "skipFallback": true, "domains": ["geosite:cn"], "expectIPs": ["geoip:cn"]}, "*******", "*******", "https://dns.google/dns-query"]}, "inbounds": [{"tag": "socks", "port": 10808, "listen": "127.0.0.1", "protocol": "mixed", "sniffing": {"enabled": true, "destOverride": ["http", "tls"], "routeOnly": false}, "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "allowTransparent": false}}], "outbounds": [{"tag": "proxy", "protocol": "vmess", "settings": {"vnext": [{"address": "***************", "port": 443, "users": [{"id": "9860b6da-7673-49c6-a606-32071ef5f9c3", "alterId": 2, "email": "<EMAIL>", "security": "auto"}]}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": false, "serverName": "cloudflare.com"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "direct", "protocol": "freedom"}, {"tag": "block", "protocol": "blackhole"}], "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "inboundTag": ["api"], "outboundTag": "api"}, {"type": "field", "port": "443", "network": "udp", "outboundTag": "block"}, {"type": "field", "outboundTag": "direct", "ip": ["geoip:private"]}, {"type": "field", "outboundTag": "direct", "domain": ["geosite:private"]}, {"type": "field", "port": "0-65535", "outboundTag": "proxy"}]}}