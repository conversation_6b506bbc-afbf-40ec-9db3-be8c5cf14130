[<PERSON>rowser]
default_browser = chrome
chrome_path = C:\Program Files\Google\Chrome\Application\chrome.exe
chrome_driver_path = C:\Users\<USER>\AppData\Local\Temp\_MEI221922\drivers\chromedriver.exe
edge_path = C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe
edge_driver_path = C:\Users\<USER>\AppData\Local\Temp\_MEI221922\drivers\msedgedriver.exe
firefox_path = C:\Program Files\Mozilla Firefox\firefox.exe
firefox_driver_path = C:\Users\<USER>\AppData\Local\Temp\_MEI221922\drivers\geckodriver.exe
brave_path = C:\Program Files\BraveSoftware/Brave-Browser/Application/brave.exe
brave_driver_path = C:\Users\<USER>\AppData\Local\Temp\_MEI221922\drivers\chromedriver.exe
opera_path = C:\Program Files\Opera\opera.exe
opera_driver_path = C:\Users\<USER>\AppData\Local\Temp\_MEI221922\drivers\chromedriver.exe
operagx_path = C:\Users\<USER>\AppData\Local\Programs\Opera GX\launcher.exe
operagx_driver_path = C:\Users\<USER>\AppData\Local\Temp\_MEI221922\drivers\chromedriver.exe

[Turnstile]
handle_turnstile_time = 2
handle_turnstile_random_time = 1-3

[Timing]
min_random_time = 0.1
max_random_time = 0.8
page_load_wait = 0.1-0.8
input_wait = 0.3-0.8
submit_wait = 0.5-1.5
verification_code_input = 0.1-0.3
verification_success_wait = 2-3
verification_retry_wait = 2-3
email_check_initial_wait = 4-6
email_refresh_wait = 2-4
settings_page_load_wait = 1-2
failed_retry_time = 0.5-1
retry_interval = 8-12
max_timeout = 160

[Utils]
enabled_update_check = True
enabled_force_update = False
enabled_account_info = True

[OAuth]
show_selection_alert = False
timeout = 120
max_attempts = 3

[Token]
refresh_server = https://token.cursorpro.com.cn
enable_refresh = True

[Language]
current_language = zh_cn
fallback_language = en
auto_update_languages = True
language_cache_dir = C:\Users\<USER>\Documents\.cursor-free-vip\language_cache

[WindowsPaths]
storage_path = C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\storage.json
sqlite_path = C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\state.vscdb
machine_id_path = C:\Users\<USER>\AppData\Roaming\Cursor\machineId
cursor_path = C:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app
updater_path = C:\Users\<USER>\AppData\Local\cursor-updater
update_yml_path = C:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app-update.yml
product_json_path = C:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\product.json

[TempMailPlus]
enabled = false
email = 
epin = 

