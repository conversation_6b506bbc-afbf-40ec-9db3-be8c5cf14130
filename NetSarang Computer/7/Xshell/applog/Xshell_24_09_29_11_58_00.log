C:\Users\<USER>\Documents\NetSarang Computer\7\Xshell\applog\Xshell_24_09_29_11_58_00.log
[11:58:00 Sunday, September 29, 2024] Log started(mode: 1)...
[11:58:00.524] Module Path: D:\Program Files\Xshell\Xshell.exe
[11:58:00.524] Module Version: ********
[11:58:00.524] Module Built: Feb 21 2023
[11:58:00.524] OS Information: Windows 10 Enterprise Build 22000
[11:58:00.547] Set Thread Locale: 2052
[11:58:00.660] Copy sample script...
[11:58:00.665] Initial DPI: 144
[11:58:00.666] Multi Core Process Mode
[11:58:00.666] True Color Terminal is Off.
[11:58:00.671] Initial UI theme: Gray
[11:58:00.684] Created Xshell main window: 2557660
[11:58:01.660] Toolbars are created.
[11:58:01.699] Font drop dialog is created.
[11:58:01.704] Checked master password.
[11:58:01.704] Xshell main window is ready.
[11:58:03.139] Setup keyboard hook...
[11:58:03.279] Loaded window layout.
[11:58:03.280] Done to set event for first run.
[15:52:30.668] Uninitialized FlexNet.
[15:52:30.668] Exit Xshell...
